#include <iostream> 
#include <string> 
#include <vector> 
#include <set> 
#include <sstream> 
#include <algorithm> 
#include <iterator>

s


std::set<int> func(const std::string& input) {
	std::set<int> resultSet;
	std::stringstream ss(input);
std::string item;
int number;

while (std::getline(ss, item, ',')) {
	number = std::stoi(item);
	resultSet.insert(number);
}
return resultSet;
}

int main() {
	std::string input_a = "1,2,2,3,4,5";
	std::string input_b = "2,4,1,2,3";

	std::set<int> ids_a = func(input_a);
	std::set<int> ids_b = func(input_b);

	std::vector<int> common_ids;
	std::set_intersection(
		ids_a.begin(), ids_a.end(),
		ids_b.begin(), ids_b.end(),
		std::back_inserter(common_ids)
	);
	std::cout << "商品 A 的购买记录: " << input_a << std::endl;
	std::cout << "商品 B 的购买记录: " << input_b << std::endl;
	std::cout << "同时购买过 A 和 B 的用户: ";
	for (size_t i = 0; i < common_ids.size(); ++i) {
		std::cout << common_ids[i] << (i == common_ids.size() - 1 ? "" : ",");
	}
	std::cout << std::endl;
	return 0;
}
