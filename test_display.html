<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮次统计显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .round-stats {
            border-radius: 12px;
            padding: 1.25rem;
            margin: 1.5rem 0;
            border-left: 4px solid;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .round-stats:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .round-stats.round-1 {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left-color: #3b82f6;
        }
        
        .round-stats h5 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .round-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: auto;
            background: #3b82f6;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            轮次统计显示测试
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- 基本统计 -->
                        <h4 class="mb-3">基本统计</h4>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value text-primary">2</div>
                                <div class="stat-label">邀请审稿人</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-warning">2</div>
                                <div class="stat-label">接受审稿</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-success">1</div>
                                <div class="stat-label">完成审稿</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value text-info">1</div>
                                <div class="stat-label">当前轮次</div>
                            </div>
                        </div>
                        
                        <!-- 轮次详细统计 -->
                        <h4 class="mb-3 mt-4">轮次详细统计</h4>
                        <div class="round-stats round-1">
                            <h5>
                                <i class="fas fa-layer-group me-2"></i>
                                第 1 轮审稿
                                <span class="round-badge">
                                    <i class="fas fa-circle"></i>
                                    Round 1
                                </span>
                            </h5>
                            <div class="d-flex gap-3 flex-wrap">
                                <span><i class="fas fa-paper-plane me-1" style="color: #3b82f6;"></i><strong>邀请:</strong> 2 人</span>
                                <span><i class="fas fa-handshake me-1" style="color: #10b981;"></i><strong>接受:</strong> 2 人</span>
                                <span><i class="fas fa-flag-checkered me-1" style="color: #f59e0b;"></i><strong>完成:</strong> 1 人</span>
                                <span><i class="fas fa-chart-pie me-1" style="color: #8b5cf6;"></i><strong>完成率:</strong> 50.0%</span>
                            </div>
                        </div>
                        
                        <!-- 说明 -->
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-info-circle me-2"></i>显示说明</h6>
                            <ul class="mb-0">
                                <li><strong>邀请:</strong> 发送审稿邀请的人数</li>
                                <li><strong>接受:</strong> 接受审稿邀请的人数</li>
                                <li><strong>完成:</strong> 已完成审稿的人数</li>
                                <li><strong>完成率:</strong> 完成人数占邀请人数的百分比</li>
                            </ul>
                        </div>
                        
                        <!-- 新格式说明 -->
                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>新格式限制</h6>
                            <p class="mb-0">
                                由于API格式变更，现在只能显示当前轮次的汇总信息，无法显示：
                                <br>• 历史轮次的详细数据
                                <br>• 具体的时间信息（邀请时间、接受时间、完成时间）
                                <br>• 个别审稿人的详细进度
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
