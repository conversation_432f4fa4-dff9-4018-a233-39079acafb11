# 🚀 极致美化版 Flask 应用 - Elsevier 稿件监控平台
# 作者: linwu | 基于原版全面重构升级
# 特色: 现代化UI设计 + 极致用户体验 + 专业级功能

from flask import Flask, render_template_string, request, redirect, url_for, session, flash, jsonify
from flask_apscheduler import APScheduler
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, IntegerField, SubmitField, SelectField
from wtforms.validators import DataRequired, Email, NumberRange
import requests
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timezone, timedelta
import json
import hashlib
import os
import urllib.parse
import logging
from functools import wraps
import threading
import time

# ============ 应用配置与初始化 ============
app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.environ.get('SECRET_KEY', 'elsevier_monitor_2025_ultra_secure_key'),
    'WTF_CSRF_ENABLED': True,
    'WTF_CSRF_TIME_LIMIT': 3600,
    'JSON_AS_ASCII': False,  # 支持中文JSON响应
    'SEND_FILE_MAX_AGE_DEFAULT': 31536000,  # 静态文件缓存1年
})

# 配置专业级日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('elsevier_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 初始化调度器
scheduler = APScheduler()
scheduler.init_app(app)
scheduler.start()

# ============ 全局配置常量 ============
CST = timezone(timedelta(hours=8))  # 北京时间
monitor_tasks = {}
system_stats = {
    'total_checks': 0,
    'total_notifications': 0,
    'uptime_start': datetime.now(CST),
    'last_check': None
}

# 环境变量配置
MAX_TASKS = int(os.environ.get('MAX_TASKS', 15))
MAX_LOG_LINES = int(os.environ.get('MAX_LOG_LINES', 100))
ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', "3313637051")
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', "aidi+6898")
API_BASE_URL = os.environ.get('API_BASE_URL', "https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker")

# 主题配色方案
THEME_COLORS = {
    'primary': '#667eea',
    'secondary': '#764ba2', 
    'success': '#10b981',
    'warning': '#f59e0b',
    'danger': '#ef4444',
    'info': '#3b82f6',
    'light': '#f8fafc',
    'dark': '#1e293b'
}

# ============ 状态映射与配置 ============
STATUS_MAPPING = {
    "1": {"text": "Submitted - 已提交", "color": "info", "icon": "fas fa-paper-plane"},
    "2": {"text": "Editor Assigned - 编辑已分配", "color": "primary", "icon": "fas fa-user-tie"},
    "3": {"text": "Under Review - 正在审稿中", "color": "warning", "icon": "fas fa-search"},
    "4": {"text": "Reviews Completed (RRC) - 审稿已完成", "color": "success", "icon": "fas fa-check-circle"},
    "5": {"text": "Decision in Progress - 编辑评估中", "color": "warning", "icon": "fas fa-hourglass-half"},
    "6": {"text": "Decision in Process - 决定中", "color": "warning", "icon": "fas fa-balance-scale"},
    "7": {"text": "Revision Required - 需要修订", "color": "warning", "icon": "fas fa-edit"},
    "8": {"text": "With Editor - 编辑评估中", "color": "primary", "icon": "fas fa-user-edit"},
    "9": {"text": "Accepted - 已接受", "color": "success", "icon": "fas fa-trophy"},
    "39": {"text": "Rejected - 已拒稿", "color": "danger", "icon": "fas fa-times-circle"},
    "29": {"text": "Decision in Progress - 编辑评估中", "color": "warning", "icon": "fas fa-hourglass-half"},
    "sub": {"text": "Submitted - 已提交", "color": "info", "icon": "fas fa-paper-plane"},
    "ur": {"text": "Under Review - 正在审稿中", "color": "warning", "icon": "fas fa-search"},
    "rrc": {"text": "Reviews Received - 等待决定", "color": "success", "icon": "fas fa-check-circle"},
    "dip": {"text": "Decision in Progress - 编辑评估中", "color": "warning", "icon": "fas fa-hourglass-half"},
    "rev": {"text": "Revision Required - 需要修订", "color": "warning", "icon": "fas fa-edit"},
    "acc": {"text": "Accepted - 已接受", "color": "success", "icon": "fas fa-trophy"},
    "rej": {"text": "Rejected - 已拒稿", "color": "danger", "icon": "fas fa-times-circle"}
}

# ============ 装饰器函数 ============
def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin'):
            flash('⚠️ 需要管理员权限才能访问此页面', 'warning')
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_calls=10, window=60):
    """简单的速率限制装饰器"""
    calls = {}
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            now = time.time()
            client_ip = request.remote_addr
            
            if client_ip not in calls:
                calls[client_ip] = []
            
            # 清理过期的调用记录
            calls[client_ip] = [call_time for call_time in calls[client_ip] if now - call_time < window]
            
            if len(calls[client_ip]) >= max_calls:
                flash('⚠️ 请求过于频繁，请稍后再试', 'warning')
                return redirect(url_for('index'))
            
            calls[client_ip].append(now)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# ============ 核心工具函数 ============
def display_time(ts):
    """智能时间显示函数"""
    if not ts:
        return None
    try:
        dt = datetime.fromtimestamp(ts, tz=timezone.utc).astimezone(CST)
        return dt
    except (ValueError, OSError) as e:
        logger.error(f"时间转换错误: {e}")
        return datetime.now(CST)

def format_duration(seconds):
    """格式化时间间隔显示"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        return f"{seconds//60}分钟"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}天{hours}小时"

def calculate_time_stats(start_time, end_time=None):
    """计算时间统计信息"""
    if not start_time:
        return {"days": 0, "hours": 0, "minutes": 0, "total_seconds": 0}
    
    start_dt = display_time(start_time)
    end_dt = display_time(end_time) if end_time else datetime.now(CST)
    
    delta = end_dt - start_dt
    total_seconds = int(delta.total_seconds())
    
    return {
        "days": delta.days,
        "hours": delta.seconds // 3600,
        "minutes": (delta.seconds % 3600) // 60,
        "total_seconds": total_seconds,
        "formatted": format_duration(total_seconds)
    }

def get_status_info(code, reviewer_map=None):
    """获取增强的状态信息"""
    code_str = str(code).lower().strip()
    
    # 特殊处理RRC状态检测
    if code_str == "3" and reviewer_map:
        revisions = [rev for (_, rev) in reviewer_map]
        max_rev = max(revisions) if revisions else None
        if max_rev is not None:
            accepted = completed = 0
            for (_, rev), events in reviewer_map.items():
                if rev != max_rev:
                    continue
                if any(ev["Event"] == "REVIEWER_ACCEPTED" for ev in events):
                    accepted += 1
                if any(ev["Event"] == "REVIEWER_COMPLETED" for ev in events):
                    completed += 1
            if accepted > 0 and accepted == completed:
                return STATUS_MAPPING.get("rrc", STATUS_MAPPING["3"])
    
    return STATUS_MAPPING.get(code_str, {
        "text": f"未知状态（{code}）",
        "color": "secondary",
        "icon": "fas fa-question-circle"
    })

def extract_uuid_from_input(raw_input):
    """智能UUID提取函数"""
    raw_input = raw_input.strip()
    
    # 处理完整URL
    if "uuid=" in raw_input:
        try:
            parsed = urllib.parse.urlparse(raw_input)
            params = urllib.parse.parse_qs(parsed.query)
            return params.get("uuid", [""])[0]
        except Exception as e:
            logger.error(f"UUID提取失败: {e}")
            return ""
    
    # 处理纯UUID
    return raw_input

def validate_email_format(email):
    """增强的邮箱格式验证"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def mask_email(email):
    """智能邮箱脱敏处理"""
    if '@' not in email or len(email) < 6:
        return email
    
    local, domain = email.split('@', 1)
    if len(local) <= 3:
        masked_local = local[0] + '*' * (len(local) - 1)
    else:
        masked_local = local[:2] + '*' * (len(local) - 4) + local[-2:]
    
    return f"{masked_local}@{domain}"

def generate_hash(obj_list):
    """生成数据哈希值用于变更检测（旧格式）"""
    filtered = [
        {"Id": ev["Id"], "Revision": ev["Revision"], "Event": ev["Event"], "Date": ev["Date"]}
        for ev in obj_list
    ]
    return hashlib.md5(json.dumps(filtered, sort_keys=True).encode()).hexdigest()

def generate_hash_from_summary(review_summary):
    """生成数据哈希值用于变更检测（新格式）"""
    # 从ReviewSummary生成哈希值
    summary_data = {
        "ReviewsCompleted": review_summary.get("ReviewsCompleted", "0"),
        "ReviewInvitationsAccepted": review_summary.get("ReviewInvitationsAccepted", "0"),
        "ReviewInvitationsSent": review_summary.get("ReviewInvitationsSent", "0")
    }
    return hashlib.md5(json.dumps(summary_data, sort_keys=True).encode()).hexdigest()

def get_status_info_from_summary(code, review_summary=None):
    """获取增强的状态信息（新格式）"""
    code_str = str(code).lower().strip()

    # 特殊处理RRC状态检测
    if code_str == "3" and review_summary:
        reviews_completed = review_summary.get("ReviewsCompleted", "0")
        invitations_accepted = review_summary.get("ReviewInvitationsAccepted", "0")

        # 尝试解析数字
        try:
            completed_count = int(reviews_completed)
            accepted_count = int(invitations_accepted)

            # 如果有接受的审稿人且全部完成了审稿
            if accepted_count > 0 and completed_count >= accepted_count:
                return STATUS_MAPPING.get("rrc", STATUS_MAPPING["3"])
        except (ValueError, TypeError):
            # 如果无法解析数字，继续使用默认状态
            pass

    return STATUS_MAPPING.get(code_str, {
        "text": f"未知状态（{code}）",
        "color": "secondary",
        "icon": "fas fa-question-circle"
    })

# ============ Flask 表单定义 ============
class EnhancedMonitorForm(FlaskForm):
    sender_email = StringField(
        '发件人邮箱',
        validators=[DataRequired(), Email()],
        render_kw={"placeholder": "<EMAIL>", "class": "form-control"}
    )
    password = PasswordField(
        '邮箱授权码',
        validators=[DataRequired()],
        render_kw={"placeholder": "16位授权码，非QQ密码", "class": "form-control"}
    )
    recipient_email = StringField(
        '收件人邮箱',
        validators=[DataRequired(), Email()],
        render_kw={"placeholder": "<EMAIL>", "class": "form-control"}
    )
    uuid = StringField(
        '稿件UUID或Track链接',
        validators=[DataRequired()],
        render_kw={"placeholder": "UUID或完整Track链接", "class": "form-control"}
    )
    interval = SelectField(
        '检查间隔',
        choices=[
            (15, '15分钟 - 高频监控'),
            (30, '30分钟 - 推荐'),
            (60, '1小时 - 标准'),
            (120, '2小时 - 低频'),
            (360, '6小时 - 最低频')
        ],
        coerce=int,
        default=30
    )
    submit = SubmitField('🚀 启动监控')

class AdminLoginForm(FlaskForm):
    username = StringField('管理员账号', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])
    submit = SubmitField('登录')

class SystemConfigForm(FlaskForm):
    max_tasks = IntegerField('最大监控任务数', validators=[DataRequired(), NumberRange(min=1, max=50)])
    max_log_lines = IntegerField('日志保留行数', validators=[DataRequired(), NumberRange(min=50, max=1000)])
    submit = SubmitField('保存设置')

# ============ 增强的日志管理系统 ============
class LogManager:
    """专业级日志管理器"""

    @staticmethod
    def write_log(recipient_email, uuid, message, log_type="info", threshold=None):
        """写入结构化日志"""
        threshold = threshold or MAX_LOG_LINES
        masked = mask_email(recipient_email)

        # 确保日志目录存在
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            logger.info(f"创建日志目录: {log_dir}")

        filename = f"{log_dir}/{masked}_{uuid}_log.json"

        now = datetime.now(CST)
        log_entry = {
            "timestamp": now.isoformat(),
            "type": log_type,
            "message": message,
            "masked_email": masked,
            "uuid": uuid
        }

        try:
            # 读取现有日志
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []

            logs.append(log_entry)

            # 保留重要日志，限制普通日志数量
            important_logs = [log for log in logs if log.get("type") in ["notification", "error"]]
            other_logs = [log for log in logs if log.get("type") not in ["notification", "error"]]

            if len(other_logs) > threshold:
                other_logs = other_logs[-threshold:]

            # 合并并保存
            final_logs = other_logs + important_logs
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(final_logs, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"日志写入失败: {e}")

    @staticmethod
    def get_logs(recipient_email, uuid, limit=50):
        """获取日志记录"""
        masked = mask_email(recipient_email)
        filename = f"logs/{masked}_{uuid}_log.json"

        logger.info(f"尝试读取日志文件: {filename}")
        logger.info(f"文件是否存在: {os.path.exists(filename)}")

        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
                logger.info(f"成功读取 {len(logs)} 条日志")
                return logs[-limit:] if limit else logs
            else:
                logger.info(f"日志文件不存在: {filename}")
        except Exception as e:
            logger.error(f"日志读取失败: {e}")

        return []

# ============ 增强的邮件发送系统 ============
class EmailManager:
    """专业级邮件管理器"""

    @staticmethod
    def send_notification_email(subject, body, sender_email, password, recipient_email, uuid=None):
        """发送美化的HTML邮件"""

        # 创建美化的HTML邮件模板
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f7fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }}
                .content {{ padding: 30px; line-height: 1.6; }}
                .footer {{ background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }}
                .status-badge {{ display: inline-block; padding: 6px 12px; border-radius: 20px; font-weight: bold; margin: 5px 0; }}
                .btn {{ display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📧 Elsevier 稿件监控通知</h1>
                    <p>专业的学术稿件状态监控服务</p>
                </div>
                <div class="content">
                    {body}
                </div>
                <div class="footer">
                    <p>© 2025 Elsevier 稿件监控平台 | 由 linwu 精心打造</p>
                    <p>本邮件由系统自动发送，请勿直接回复</p>
                </div>
            </div>
        </body>
        </html>
        """

        msg = MIMEText(html_body, 'html', 'utf-8')
        msg['Subject'] = subject
        msg['From'] = sender_email
        msg['To'] = recipient_email

        try:
            if not sender_email.endswith('@qq.com'):
                raise ValueError("仅支持 QQ 邮箱作为发件人")

            if len(password) < 10:
                raise ValueError("QQ邮箱授权码长度应为16位字符")

            server = smtplib.SMTP('smtp.qq.com', 587)
            server.starttls()
            server.login(sender_email, password)
            server.sendmail(sender_email, [recipient_email], msg.as_string())
            server.quit()

            if uuid:
                LogManager.write_log(recipient_email, uuid, f"📧 邮件发送成功: {subject}", "notification")

            # 更新系统统计
            system_stats['total_notifications'] += 1

            return True, "发送成功"

        except smtplib.SMTPAuthenticationError:
            error_msg = "❌ 邮箱认证失败：请检查QQ邮箱地址和授权码是否正确"
        except smtplib.SMTPConnectError:
            error_msg = "❌ 无法连接到QQ邮箱服务器，请检查网络连接"
        except smtplib.SMTPRecipientsRefused:
            error_msg = "❌ 收件人邮箱地址被拒绝，请检查邮箱格式"
        except Exception as e:
            error_str = str(e)
            if "535" in error_str:
                error_msg = "❌ QQ邮箱授权码错误：请确保已开启SMTP服务并使用正确的16位授权码"
            elif "550" in error_str:
                error_msg = "❌ 邮箱地址格式错误或不存在"
            elif "timeout" in error_str.lower():
                error_msg = "❌ 连接超时，请检查网络连接或稍后重试"
            else:
                error_msg = f"❌ 邮件发送失败: {error_str}"

        if uuid:
            LogManager.write_log(recipient_email, uuid, f"❌ 邮件发送失败: {error_msg}", "error")

        return False, error_msg

# ============ 增强的监控任务管理器 ============
class TaskManager:
    """专业级任务管理器"""

    @staticmethod
    def validate_monitoring_config(sender_email, password, recipient_email, interval):
        """验证监控配置参数"""
        errors = []

        # 验证发件人邮箱
        if not sender_email.endswith('@qq.com'):
            errors.append("❌ 发件人邮箱：仅支持QQ邮箱（@qq.com）作为发件人")

        # 验证授权码
        if len(password.strip()) < 10:
            errors.append("❌ 授权码格式错误：QQ邮箱授权码应为16位字符，不是QQ密码")

        # 验证收件人邮箱
        if not validate_email_format(recipient_email):
            errors.append("❌ 收件人邮箱格式不正确")

        # 验证监控间隔
        if interval < 5:
            errors.append("❌ 监控间隔太短：为避免频繁请求，建议间隔至少5分钟")
        elif interval > 1440:  # 24小时
            errors.append("❌ 监控间隔太长：建议间隔不超过24小时（1440分钟）")

        return errors

    @staticmethod
    def check_manuscript_status(task_id):
        """检查单个监控任务的状态 - 增强版"""
        task = monitor_tasks.get(task_id)
        if not task:
            logger.warning(f"任务 {task_id} 不存在")
            return

        try:
            url = f"{API_BASE_URL}/{task['uuid']}"
            response = requests.get(url, timeout=15)
            response.raise_for_status()

            data = response.json()
            if not isinstance(data, dict) or "Status" not in data:
                raise ValueError("响应格式不正确或不包含 Status 字段")

            # 获取当前状态信息
            cur_status = data.get("Status")
            cur_updated = data.get("LastUpdated")

            # 新格式：使用 ReviewSummary 而不是 ReviewEvents
            review_summary = data.get("ReviewSummary", {})
            current_review_hash = generate_hash_from_summary(review_summary)

            status_info = get_status_info_from_summary(cur_status, review_summary)

            # 记录检查日志
            LogManager.write_log(
                task['recipient_email'],
                task['uuid'],
                f"🔍 定时检查完成 - 状态: {status_info['text']} | 更新时间: {display_time(cur_updated).strftime('%Y-%m-%d %H:%M:%S')}",
                "check"
            )

            # 检测变化
            should_notify = False
            change_reasons = []

            if task['last_status'] != cur_status:
                should_notify = True
                change_reasons.append("📊 状态码变化")

            if task['last_updated'] != cur_updated:
                should_notify = True
                change_reasons.append("⏰ 更新时间变化")

            if task.get("review_hash") != current_review_hash:
                should_notify = True
                change_reasons.append("👥 审稿人信息变化")

            # 检查RRC状态（新格式）
            if cur_status == 3 and review_summary:
                try:
                    reviews_completed = int(review_summary.get("ReviewsCompleted", "0"))
                    invitations_accepted = int(review_summary.get("ReviewInvitationsAccepted", "0"))

                    # 如果有接受的审稿人且全部完成了审稿
                    if invitations_accepted > 0 and reviews_completed >= invitations_accepted:
                        should_notify = True
                        change_reasons.append("✅ 本轮所有审稿人已完成（RRC）")
                except (ValueError, TypeError):
                    # 如果无法解析数字，跳过RRC检查
                    pass

            # 发送通知
            if should_notify:
                TaskManager._send_status_change_notification(task, data, status_info, change_reasons)

                # 更新任务状态
                task['last_status'] = cur_status
                task['last_updated'] = cur_updated
                task['review_hash'] = current_review_hash

            # 重置失败计数
            task['fail_count'] = 0

            # 更新系统统计
            system_stats['total_checks'] += 1
            system_stats['last_check'] = datetime.now(CST)

        except Exception as e:
            TaskManager._handle_task_failure(task_id, task, e)

    @staticmethod
    def _send_status_change_notification(task, data, status_info, change_reasons):
        """发送状态变化通知"""
        subject = "📢 稿件状态更新通知"

        # 计算时间统计
        submission_stats = calculate_time_stats(data.get("SubmissionDate"))

        body = f"""
        <div style="margin: 20px 0;">
            <h2 style="color: #667eea; margin-bottom: 20px;">📄 稿件信息</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>📝 稿件标题：</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">{data.get('ManuscriptTitle', '未知')}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>🔢 稿件编号：</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">{data.get('PubdNumber', '未知')}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>📚 投稿期刊：</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">{data.get('JournalName', '未知')}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>👨‍🔬 第一作者：</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">{data.get('FirstAuthor', '未知')}</td></tr>
                <tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>📧 通讯作者：</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">{data.get('CorrespondingAuthor', '未知')}</td></tr>
            </table>
        </div>

        <div style="margin: 20px 0;">
            <h2 style="color: #667eea; margin-bottom: 20px;">📊 状态信息</h2>
            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 18px;"><strong>📌 当前状态：</strong>
                <span class="status-badge" style="background: #{status_info.get('color', 'secondary')}; color: white;">{status_info['text']}</span></p>
            </div>
            <p><strong>🕓 最近更新：</strong> {display_time(data.get('LastUpdated')).strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>📅 投稿时间：</strong> {display_time(data.get('SubmissionDate')).strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>⏱️ 总耗时：</strong> {submission_stats['formatted']}</p>
        </div>

        <div style="margin: 20px 0;">
            <h2 style="color: #667eea; margin-bottom: 20px;">🔍 变动详情</h2>
            <ul style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                {''.join([f'<li>{reason}</li>' for reason in change_reasons])}
            </ul>
        </div>

        <div style="margin: 20px 0; text-align: center;">
            <p style="color: #6c757d;">⏰ 检查时间：{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')} (北京时间)</p>
            <p style="color: #6c757d;">📡 系统将继续每 {task['interval']} 分钟自动检测一次</p>
        </div>
        """

        success, error = EmailManager.send_notification_email(
            subject, body, task['sender_email'], task['password'], task['recipient_email'], task['uuid']
        )

        if success:
            LogManager.write_log(
                task['recipient_email'],
                task['uuid'],
                f"📢 通知发送成功 - 变动原因: {' | '.join(change_reasons)}",
                "notification"
            )
        else:
            LogManager.write_log(
                task['recipient_email'],
                task['uuid'],
                f"❌ 通知发送失败: {error}",
                "error"
            )

    @staticmethod
    def _handle_task_failure(task_id, task, error):
        """处理任务失败"""
        task.setdefault('fail_count', 0)
        task['fail_count'] += 1

        if task['fail_count'] >= 3:
            LogManager.write_log(
                task['recipient_email'],
                task['uuid'],
                f"❌ 连续失败 {task['fail_count']} 次，任务自动终止: {error}",
                "error"
            )

            # 移除调度任务
            try:
                scheduler.remove_job(task_id)
            except:
                pass

            # 删除任务记录
            if task_id in monitor_tasks:
                del monitor_tasks[task_id]
        else:
            LogManager.write_log(
                task['recipient_email'],
                task['uuid'],
                f"⚠️ 第 {task['fail_count']} 次检查失败: {error}",
                "warning"
            )

# ============ Flask 路由 - 主页面 ============
@app.route('/')
def index():
    """现代化主页面"""
    form = EnhancedMonitorForm()
    can_submit = len(monitor_tasks) < MAX_TASKS

    # 计算系统统计信息
    uptime = datetime.now(CST) - system_stats['uptime_start']
    uptime_str = format_duration(int(uptime.total_seconds()))

    stats = {
        'active_tasks': len(monitor_tasks),
        'max_tasks': MAX_TASKS,
        'total_checks': system_stats['total_checks'],
        'total_notifications': system_stats['total_notifications'],
        'uptime': uptime_str,
        'last_check': system_stats['last_check'].strftime('%H:%M:%S') if system_stats['last_check'] else '未开始'
    }

    return render_template_string(ENHANCED_MAIN_TEMPLATE,
                                  form=form,
                                  monitor_tasks=monitor_tasks,
                                  admin=session.get("admin", False),
                                  can_submit=can_submit,
                                  stats=stats,
                                  theme_colors=THEME_COLORS,
                                  mask_email=mask_email)

@app.route('/start_monitoring', methods=['POST'])
@rate_limit(max_calls=5, window=300)  # 5分钟内最多5次请求
def start_monitoring():
    """启动监控任务 - 增强版"""
    logger.info("收到监控任务启动请求")
    try:
        form = EnhancedMonitorForm()

        if not form.validate_on_submit():
            # 详细的表单验证错误信息
            error_messages = []
            for field, errors in form.errors.items():
                for error in errors:
                    error_messages.append(f"{getattr(form, field).label.text}: {error}")

            if error_messages:
                flash(f'❌ 表单验证失败: {"; ".join(error_messages)}', 'danger')
            else:
                flash('❌ 表单验证失败，请检查输入信息', 'danger')
            return redirect(url_for('index'))

        if len(monitor_tasks) >= MAX_TASKS:
            flash(f'❌ 已达到最大任务数限制（{MAX_TASKS}个）', 'warning')
            return redirect(url_for('index'))

        # 验证配置参数
        config_errors = TaskManager.validate_monitoring_config(
            form.sender_email.data,
            form.password.data,
            form.recipient_email.data,
            form.interval.data
        )

        if config_errors:
            flash(config_errors[0], 'danger')
            return redirect(url_for('index'))

        # 提取和验证UUID
        raw_uuid = form.uuid.data.strip()
        uuid = extract_uuid_from_input(raw_uuid)

        if not uuid or len(uuid.strip()) < 10:
            flash('❌ UUID格式错误：请输入完整的UUID或Track链接', 'danger')
            return redirect(url_for('index'))

        # 检查重复监控
        for task in monitor_tasks.values():
            if task['recipient_email'] == form.recipient_email.data and task['uuid'] == uuid:
                flash('⚠️ 重复监控：当前收件人已经在监控该稿件', 'warning')
                return redirect(url_for('index'))

        # 创建任务
        task_id = f"task_{int(time.time())}_{len(monitor_tasks)}"
        task = {
            'sender_email': form.sender_email.data.strip(),
            'password': form.password.data.strip(),
            'recipient_email': form.recipient_email.data.strip(),
            'uuid': uuid.strip(),
            'interval': form.interval.data,
            'last_status': None,
            'last_updated': None,
            'review_hash': None,
            'fail_count': 0,
            'created_at': datetime.now(CST),
            'task_id': task_id
        }

        # 验证UUID有效性
        url = f"{API_BASE_URL}/{task['uuid']}"
        response = requests.get(url, timeout=15)
        response.raise_for_status()

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("UUID无效：该UUID对应的稿件不存在或无法访问")

        # 初始化任务状态
        task['last_status'] = data.get("Status")
        task['last_updated'] = data.get("LastUpdated")
        task["review_hash"] = generate_hash_from_summary(data.get("ReviewSummary", {}))

        # 发送启动确认邮件
        success, error_msg = _send_startup_confirmation_email(task, data)
        if not success:
            flash(f'❌ 邮件发送失败: {error_msg}', 'danger')
            return redirect(url_for('index'))

        # 添加到监控任务列表
        monitor_tasks[task_id] = task

        # 启动定时任务
        scheduler.add_job(
            id=task_id,
            func=TaskManager.check_manuscript_status,
            args=[task_id],
            trigger='interval',
            minutes=task['interval'],
            max_instances=3,
            coalesce=True
        )

        # 记录启动日志
        LogManager.write_log(
            task['recipient_email'],
            task['uuid'],
            f"🚀 监控任务启动成功 - 间隔: {task['interval']}分钟",
            "info"
        )

        flash('✅ 监控任务启动成功！已发送确认邮件，请查收', 'success')

    except requests.exceptions.Timeout:
        flash('❌ 网络超时：连接Elsevier服务器超时，请检查网络连接', 'danger')
    except requests.exceptions.ConnectionError:
        flash('❌ 网络连接失败：无法连接到Elsevier服务器', 'danger')
    except Exception as e:
        error_str = str(e)
        logger.error(f"监控任务启动失败: {error_str}")
        if "UUID" in error_str or "uuid" in error_str:
            flash(f'❌ UUID验证失败：{error_str}', 'danger')
        else:
            flash(f'❌ 启动失败：{error_str}', 'danger')

    return redirect(url_for('index'))

def _send_startup_confirmation_email(task, data):
    """发送启动确认邮件"""
    subject = "🚀 稿件监控已启动"

    # 获取状态信息（新格式）
    review_summary = data.get("ReviewSummary", {})
    status_info = get_status_info_from_summary(data.get("Status"), review_summary)
    submission_stats = calculate_time_stats(data.get("SubmissionDate"))

    body = f"""
    <div style="text-align: center; margin: 20px 0;">
        <h1 style="color: #667eea;">🎉 监控任务启动成功！</h1>
        <p style="font-size: 18px; color: #6c757d;">您的稿件监控服务已正式开始</p>
    </div>

    <div style="margin: 20px 0;">
        <h2 style="color: #667eea;">📄 稿件信息</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;">
            <p><strong>📝 稿件标题：</strong> {data.get('ManuscriptTitle', '未知')}</p>
            <p><strong>🔢 稿件编号：</strong> {data.get('PubdNumber', '未知')}</p>
            <p><strong>📚 投稿期刊：</strong> {data.get('JournalName', '未知')}</p>
            <p><strong>👨‍🔬 第一作者：</strong> {data.get('FirstAuthor', '未知')}</p>
            <p><strong>📧 通讯作者：</strong> {data.get('CorrespondingAuthor', '未知')}</p>
        </div>
    </div>

    <div style="margin: 20px 0;">
        <h2 style="color: #667eea;">📊 当前状态</h2>
        <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 20px; border-radius: 8px;">
            <p style="font-size: 16px; margin: 0;"><strong>状态：</strong>
            <span style="background: #{status_info.get('color', 'info')}; color: white; padding: 6px 12px; border-radius: 20px; font-weight: bold;">{status_info['text']}</span></p>
            <p><strong>更新时间：</strong> {display_time(data.get('LastUpdated')).strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>投稿时间：</strong> {display_time(data.get('SubmissionDate')).strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>总耗时：</strong> {submission_stats['formatted']}</p>
        </div>
    </div>

    <div style="margin: 20px 0;">
        <h2 style="color: #667eea;">⚙️ 监控设置</h2>
        <ul style="background: #fff3cd; padding: 15px; border-radius: 8px;">
            <li><strong>监控频率：</strong> 每 {task['interval']} 分钟自动检测一次</li>
            <li><strong>通知条件：</strong> 状态变化、更新时间变化、审稿人信息变化</li>
            <li><strong>特殊检测：</strong> 自动识别RRC（Reviews Received）状态</li>
        </ul>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <p style="color: #28a745; font-weight: bold;">✅ 系统已开始24小时不间断监控</p>
        <p style="color: #6c757d;">启动时间：{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')} (北京时间)</p>
    </div>
    """

    return EmailManager.send_notification_email(subject, body, task['sender_email'], task['password'], task['recipient_email'], task['uuid'])

# ============ 管理员功能路由 ============
@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录"""
    form = AdminLoginForm()
    if form.validate_on_submit():
        if form.username.data == ADMIN_USERNAME and form.password.data == ADMIN_PASSWORD:
            session['admin'] = True
            session.permanent = True
            flash('✅ 管理员登录成功', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash('❌ 用户名或密码错误', 'danger')

    return render_template_string(ADMIN_LOGIN_TEMPLATE, form=form)

@app.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    """管理员仪表板"""
    # 计算详细统计信息
    uptime = datetime.now(CST) - system_stats['uptime_start']

    # 任务状态统计
    task_stats = {
        'total': len(monitor_tasks),
        'active': len([t for t in monitor_tasks.values() if t.get('fail_count', 0) == 0]),
        'failed': len([t for t in monitor_tasks.values() if t.get('fail_count', 0) > 0])
    }

    # 系统统计
    stats = {
        'uptime': format_duration(int(uptime.total_seconds())),
        'total_checks': system_stats['total_checks'],
        'total_notifications': system_stats['total_notifications'],
        'last_check': system_stats['last_check'].strftime('%Y-%m-%d %H:%M:%S') if system_stats['last_check'] else '未开始',
        'task_stats': task_stats
    }

    return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                  monitor_tasks=monitor_tasks,
                                  stats=stats,
                                  max_tasks=MAX_TASKS,
                                  max_log_lines=MAX_LOG_LINES,
                                  mask_email=mask_email)

@app.route('/admin/delete_task/<task_id>', methods=['POST'])
@admin_required
def delete_task(task_id):
    """删除监控任务"""
    if task_id in monitor_tasks:
        task = monitor_tasks[task_id]

        # 记录删除日志
        LogManager.write_log(
            task['recipient_email'],
            task['uuid'],
            f"🗑️ 管理员删除任务 {task_id}",
            "admin"
        )

        # 停止定时任务
        try:
            scheduler.remove_job(task_id)
        except:
            pass

        # 删除日志文件
        try:
            masked_email = mask_email(task['recipient_email'])
            log_filename = f"logs/{masked_email}_{task['uuid']}_log.json"
            if os.path.exists(log_filename):
                os.remove(log_filename)
                logger.info(f"删除日志文件: {log_filename}")
        except Exception as e:
            logger.error(f"删除日志文件失败: {e}")

        # 删除任务记录
        del monitor_tasks[task_id]

        flash(f'✅ 任务 {task_id} 及相关日志已删除', 'success')
    else:
        flash(f'❌ 任务 {task_id} 不存在', 'warning')

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/config', methods=['GET', 'POST'])
@admin_required
def admin_config():
    """系统配置"""
    global MAX_TASKS, MAX_LOG_LINES

    form = SystemConfigForm()
    if form.validate_on_submit():
        MAX_TASKS = form.max_tasks.data
        MAX_LOG_LINES = form.max_log_lines.data
        flash('✅ 系统配置已更新', 'success')
        return redirect(url_for('admin_dashboard'))

    form.max_tasks.data = MAX_TASKS
    form.max_log_lines.data = MAX_LOG_LINES

    return render_template_string(ADMIN_CONFIG_TEMPLATE, form=form)

@app.route('/admin/logs/<path:masked_email>/<path:uuid>')
@admin_required
def view_logs(masked_email, uuid):
    """查看任务日志"""
    logger.info(f"查看日志请求: masked_email={masked_email}, uuid={uuid}")

    # 查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        task_masked = mask_email(task['recipient_email'])
        logger.info(f"比较邮箱: {task_masked} vs {masked_email}")
        if task_masked == masked_email:
            real_email = task['recipient_email']
            break

    if not real_email:
        logger.warning(f"无法找到对应邮箱: {masked_email}")
        logger.info(f"当前任务列表: {[mask_email(task['recipient_email']) for task in monitor_tasks.values()]}")
        flash(f'❌ 无法找到对应邮箱: {masked_email}', 'warning')
        return redirect(url_for('admin_dashboard'))

    logger.info(f"找到真实邮箱: {real_email}")
    logs = LogManager.get_logs(real_email, uuid, limit=100)
    logger.info(f"获取到 {len(logs)} 条日志")

    return render_template_string(ADMIN_LOGS_TEMPLATE,
                                  logs=logs,
                                  masked_email=masked_email,
                                  uuid=uuid)

@app.route('/admin/clear_logs/<path:masked_email>/<path:uuid>', methods=['POST'])
@admin_required
def clear_logs(masked_email, uuid):
    """清空任务日志"""
    # 查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        if mask_email(task['recipient_email']) == masked_email:
            real_email = task['recipient_email']
            break

    if real_email:
        filename = f"logs/{masked_email}_{uuid}_log.json"
        try:
            if os.path.exists(filename):
                os.remove(filename)
            flash('✅ 日志已清空', 'success')
        except Exception as e:
            flash(f'❌ 日志清空失败: {e}', 'danger')
    else:
        flash('❌ 无法识别的邮箱标识', 'warning')

    return redirect(url_for('view_logs', masked_email=masked_email, uuid=uuid))

@app.route('/admin/logout')
def admin_logout():
    """管理员退出"""
    session.pop('admin', None)
    flash('✅ 已退出管理员模式', 'info')
    return redirect(url_for('index'))

# ============ UUID 查询功能 ============
@app.route('/query', methods=['GET', 'POST'])
@rate_limit(max_calls=20, window=300)  # 5分钟内最多20次查询
def query_manuscript():
    """稿件状态查询入口"""
    if request.method == "POST":
        raw_input = request.form.get("uuid", "").strip()
        uuid = extract_uuid_from_input(raw_input)
        if uuid:
            return redirect(url_for('query_result', uuid=uuid))
        else:
            flash('❌ 请输入有效的UUID或Track链接', 'warning')

    return render_template_string(QUERY_INPUT_TEMPLATE)

@app.route('/result')
def query_result():
    """显示查询结果"""
    raw_uuid = request.args.get("uuid", "").strip()
    uuid = extract_uuid_from_input(raw_uuid)

    if not uuid:
        flash('❌ 无效的UUID参数', 'danger')
        return redirect(url_for('query_manuscript'))

    try:
        api_url = f"{API_BASE_URL}/{uuid}"
        response = requests.get(api_url, timeout=15)
        response.raise_for_status()

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("数据格式错误")

        # 处理审稿人数据（新格式）
        review_summary = data.get("ReviewSummary", {})

        # 从ReviewSummary获取统计信息
        invited_count = review_summary.get("ReviewInvitationsSent", "0")
        accepted_count = review_summary.get("ReviewInvitationsAccepted", "0")
        completed_count = review_summary.get("ReviewsCompleted", "0")

        # 计算时间统计
        submission_stats = calculate_time_stats(data.get("SubmissionDate"))
        last_update_stats = calculate_time_stats(data.get("LastUpdated"))

        # 由于新格式没有详细的审稿人事件数据，我们只能显示汇总信息
        reviewers = []

        # 尝试解析数字
        try:
            invited_num = int(invited_count.replace('+', '')) if isinstance(invited_count, str) else int(invited_count)
            accepted_num = int(accepted_count) if isinstance(accepted_count, str) else int(accepted_count)
            completed_num = int(completed_count) if isinstance(completed_count, str) else int(completed_count)
        except (ValueError, TypeError):
            invited_num = accepted_num = completed_num = 0

        # 获取状态信息（新格式）
        status_info = get_status_info_from_summary(data.get("Status"), review_summary)

        # 由于新格式没有轮次信息，我们使用默认值
        group_stats = {
            data.get("LatestRevisionNumber", 0): {
                "invited": invited_num,
                "accepted": accepted_num,
                "completed": completed_num
            }
        }
        current_round = data.get("LatestRevisionNumber", 0) + 1

        return render_template_string(QUERY_RESULT_TEMPLATE,
                                      title=data.get("ManuscriptTitle"),
                                      pubd_number=data.get("PubdNumber"),
                                      journal=data.get("JournalName"),
                                      first_author=data.get("FirstAuthor"),
                                      corresponding_author=data.get("CorrespondingAuthor"),
                                      uuid=uuid,
                                      status_info=status_info,
                                      submission_stats=submission_stats,
                                      last_update_stats=last_update_stats,
                                      updated=display_time(data.get("LastUpdated")).strftime('%Y-%m-%d %H:%M:%S'),
                                      submitted=display_time(data.get("SubmissionDate")).strftime('%Y-%m-%d %H:%M:%S'),
                                      invited_count=invited_count,
                                      accepted_count=accepted_count,
                                      completed_count=completed_count,
                                      reviewers=reviewers,
                                      group_stats=group_stats,
                                      current_round=current_round,
                                      query_time=datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S'))

    except Exception as e:
        logger.error(f"查询失败: {e}")
        return render_template_string(QUERY_ERROR_TEMPLATE, error=str(e))

# ============ API 接口 ============
@app.route('/api/stats')
def api_stats():
    """系统统计API"""
    uptime = datetime.now(CST) - system_stats['uptime_start']

    return jsonify({
        'active_tasks': len(monitor_tasks),
        'max_tasks': MAX_TASKS,
        'total_checks': system_stats['total_checks'],
        'total_notifications': system_stats['total_notifications'],
        'uptime_seconds': int(uptime.total_seconds()),
        'last_check': system_stats['last_check'].isoformat() if system_stats['last_check'] else None
    })

@app.route('/api/health')
def api_health():
    """健康检查API"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now(CST).isoformat(),
        'version': '2.0.0-enhanced'
    })

@app.route('/api/debug/tasks')
@admin_required
def debug_tasks():
    """调试任务信息"""
    debug_info = []
    for task_id, task in monitor_tasks.items():
        debug_info.append({
            'task_id': task_id,
            'real_email': task['recipient_email'],
            'masked_email': mask_email(task['recipient_email']),
            'uuid': task['uuid']
        })
    return jsonify(debug_info)

# ============ 现代化HTML模板 ============

# 主页面模板
ENHANCED_MAIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>🚀 Elsevier 稿件监控平台 - 极致版</title>

    <!-- 移动端优化 -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Elsevier监控">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-TileColor" content="#667eea">

    <!-- 现代化CSS框架 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: {{ theme_colors.primary }};
            --secondary-color: {{ theme_colors.secondary }};
            --success-color: {{ theme_colors.success }};
            --warning-color: {{ theme_colors.warning }};
            --danger-color: {{ theme_colors.danger }};
            --info-color: {{ theme_colors.info }};
            --light-color: {{ theme_colors.light }};
            --dark-color: {{ theme_colors.dark }};

            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--dark-color);
        }

        /* 主容器样式 */
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 头部样式 */
        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* 统计卡片样式 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* 表单样式 */
        .form-section {
            padding: 2rem;
        }

        .form-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        /* 移动端输入框优化 */
        @media (max-width: 768px) {
            .form-control {
                font-size: 16px; /* 防止iOS缩放 */
                padding: 1rem;
                border-radius: 10px;
                min-height: 48px; /* 触摸友好的最小高度 */
            }

            .form-control:focus {
                transform: none; /* 移动端不需要上移动画 */
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
            }
        }

        .form-text {
            font-size: 0.85rem;
            color: #64748b;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.875rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            min-height: 44px; /* iOS推荐的最小触摸目标 */
            -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
            user-select: none;
        }

        /* 移动端按钮优化 */
        @media (max-width: 768px) {
            .btn {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                min-height: 48px;
                border-radius: 10px;
            }

            .btn-sm {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
                min-height: 40px;
            }

            .btn:active {
                transform: scale(0.98);
            }
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline-primary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .btn-outline-danger {
            background: white;
            color: var(--danger-color);
            border: 2px solid var(--danger-color);
        }

        .btn-outline-danger:hover {
            background: var(--danger-color);
            color: white;
            transform: translateY(-2px);
        }

        /* 任务列表样式 */
        .tasks-section {
            padding: 2rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
            position: relative;
            overflow: hidden;
        }

        .tasks-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .task-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .task-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            background: rgba(255, 255, 255, 0.95);
        }

        .task-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            border-radius: 16px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .task-item:hover::before {
            opacity: 1;
        }

        /* 移动端任务卡片优化 */
        @media (max-width: 768px) {
            .task-item {
                padding: 1.25rem;
                border-radius: 12px;
                margin-bottom: 0.75rem;
            }

            .task-item:hover {
                transform: none; /* 移动端不需要悬停效果 */
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }

            .task-item:active {
                transform: scale(0.99);
                transition: transform 0.1s ease;
            }

            .task-item::before {
                display: none; /* 移动端不需要悬停背景 */
            }
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            position: relative;
        }

        .empty-icon-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }

        .empty-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin: 0 auto;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            animation: float 3s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }

        .empty-particles {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
            animation: orbit 4s linear infinite;
        }

        .particle:nth-child(1) {
            animation-delay: 0s;
            top: 20px;
            left: 50%;
        }

        .particle:nth-child(2) {
            animation-delay: 1.3s;
            top: 50%;
            right: 20px;
        }

        .particle:nth-child(3) {
            animation-delay: 2.6s;
            bottom: 20px;
            left: 50%;
        }

        .empty-title {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .empty-subtitle {
            color: #64748b;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .empty-features {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .feature-item:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .feature-item i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .feature-item span {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes orbit {
            0% {
                transform: rotate(0deg) translateX(80px) rotate(0deg);
            }
            100% {
                transform: rotate(360deg) translateX(80px) rotate(-360deg);
            }
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }

        .task-info {
            flex: 1;
        }

        .task-info h5 {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .task-status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .task-status-normal {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .task-status-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .task-actions {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        .task-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
        }

        .task-meta-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
        }

        .task-meta-item:hover {
            background: rgba(248, 250, 252, 1);
            transform: translateY(-1px);
        }

        .task-meta-item i {
            width: 20px;
            text-align: center;
            color: var(--primary-color);
            font-size: 1rem;
        }

        .task-meta-item span {
            font-size: 0.9rem;
            color: #475569;
            font-weight: 500;
        }

        /* 警告框样式 */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: var(--shadow-sm);
        }

        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border-left: 4px solid var(--danger-color);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-left: 4px solid var(--warning-color);
        }

        .alert-info {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border-left: 4px solid var(--info-color);
        }

        /* 导航按钮 */
        .nav-buttons {
            padding: 2rem;
            background: white;
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        /* 移动端滚动优化 */
        @media (max-width: 768px) {
            body {
                -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
                overflow-x: hidden;
            }

            .task-list {
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            /* 隐藏滚动条但保持功能 */
            .task-list::-webkit-scrollbar {
                width: 0px;
                background: transparent;
            }
            .main-container {
                margin: 0.5rem;
                border-radius: 16px;
            }

            .header {
                padding: 1.5rem 1rem;
            }

            .header h1 {
                font-size: 1.75rem;
                line-height: 1.2;
            }

            .header p {
                font-size: 1rem;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
                padding: 1rem;
                gap: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .stat-value {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .form-section,
            .tasks-section {
                padding: 1rem;
            }

            .form-container {
                padding: 1.5rem;
            }

            .form-control {
                padding: 0.75rem;
                font-size: 16px; /* 防止iOS缩放 */
            }

            .btn {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }

            .task-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding-bottom: 0.75rem;
            }

            .task-actions {
                align-self: flex-end;
                gap: 0.5rem;
                width: 100%;
                justify-content: flex-end;
                flex-wrap: wrap;
            }

            .task-actions .btn {
                min-width: 44px;
                padding: 0.75rem;
                border-radius: 8px;
            }

            .task-actions .btn i {
                font-size: 1rem;
            }

            .task-meta {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .task-meta-item {
                padding: 0.5rem 0.75rem;
                font-size: 0.85rem;
            }

            .empty-icon {
                width: 80px;
                height: 80px;
                font-size: 2rem;
            }

            .empty-particles {
                width: 140px;
                height: 140px;
            }

            .particle {
                width: 5px;
                height: 5px;
            }

            .empty-title {
                font-size: 1.25rem;
            }

            .empty-subtitle {
                font-size: 0.9rem;
            }

            .nav-buttons {
                padding: 1rem;
                gap: 0.5rem;
            }

            .nav-buttons .btn {
                font-size: 0.9rem;
                padding: 0.6rem 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                margin: 0.25rem;
                border-radius: 12px;
            }

            .header {
                padding: 1rem 0.75rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .stats-container {
                grid-template-columns: 1fr;
                padding: 0.75rem;
                gap: 0.5rem;
            }

            .stat-card {
                padding: 0.75rem;
                text-align: left;
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .stat-icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
                margin: 0;
                flex-shrink: 0;
            }

            .stat-content {
                flex: 1;
            }

            .stat-value {
                font-size: 1.25rem;
                margin-bottom: 0.25rem;
            }

            .stat-label {
                font-size: 0.75rem;
            }

            .form-section,
            .tasks-section {
                padding: 0.75rem;
            }

            .form-container {
                padding: 1rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-label {
                font-size: 0.95rem;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #374151;
            }

            .form-text {
                font-size: 0.8rem;
                margin-top: 0.4rem;
                line-height: 1.4;
                color: #6b7280;
            }

            .task-status-badge {
                font-size: 0.65rem;
                padding: 0.2rem 0.5rem;
            }

            .task-info h5 {
                font-size: 0.95rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .task-meta-item {
                font-size: 0.8rem;
                padding: 0.6rem 0.8rem;
                border-radius: 8px;
                min-height: 40px;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .task-meta-item i {
                font-size: 0.9rem;
                flex-shrink: 0;
                width: 16px;
                text-align: center;
            }

            .task-meta-item span {
                word-break: break-all;
                line-height: 1.3;
            }

            .empty-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .empty-particles {
                width: 100px;
                height: 100px;
            }

            .particle {
                width: 4px;
                height: 4px;
            }

            .empty-title {
                font-size: 1.1rem;
            }

            .empty-subtitle {
                font-size: 0.85rem;
            }

            .nav-buttons {
                padding: 0.75rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .nav-buttons .btn {
                width: 100%;
                font-size: 0.9rem;
                padding: 0.8rem 1rem;
                min-height: 48px;
                border-radius: 10px;
                font-weight: 500;
            }

            /* 添加触摸反馈 */
            .nav-buttons .btn:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
        }

        /* 加载动画 */
        .loading {
            position: relative;
            color: transparent !important;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* 脉冲动画 */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部区域 -->
            <div class="header">
                <h1><i class="fas fa-rocket me-3"></i>Elsevier 稿件监控平台</h1>
                <p>专业级学术稿件状态监控服务 - 极致版</p>
            </div>

            <!-- 系统统计 -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-primary);">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.active_tasks }}/{{ stats.max_tasks }}</div>
                        <div class="stat-label">活跃任务</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-success);">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.total_checks }}</div>
                        <div class="stat-label">总检查次数</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-warning);">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.total_notifications }}</div>
                        <div class="stat-label">发送通知</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--gradient-danger);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ stats.uptime }}</div>
                        <div class="stat-label">运行时间</div>
                    </div>
                </div>
            </div>

            <!-- Flash 消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div style="padding: 2rem 2rem 0;">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}">
                                <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'danger' %}exclamation-triangle{% elif category == 'warning' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            <div class="row g-0">
                <!-- 监控表单 -->
                <div class="col-lg-6">
                    <div class="form-section">
                        <div class="form-container">
                            <h3 class="mb-4">
                                <i class="fas fa-satellite-dish me-2" style="color: var(--primary-color);"></i>
                                启动监控任务
                            </h3>

                            <form method="post" action="{{ url_for('start_monitoring') }}" id="monitorForm">
                                {{ form.hidden_tag() }}

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope me-2"></i>{{ form.sender_email.label.text }}
                                    </label>
                                    {{ form.sender_email(class="form-control") }}
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        仅支持QQ邮箱，需开启SMTP服务获取授权码
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-key me-2"></i>{{ form.password.label.text }}
                                    </label>
                                    {{ form.password(class="form-control") }}
                                    <div class="form-text">
                                        <i class="fas fa-shield-alt"></i>
                                        在QQ邮箱设置→账户→开启SMTP服务获取16位授权码
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-inbox me-2"></i>{{ form.recipient_email.label.text }}
                                    </label>
                                    {{ form.recipient_email(class="form-control") }}
                                    <div class="form-text">
                                        <i class="fas fa-globe"></i>
                                        支持所有邮箱，用于接收稿件状态通知
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-link me-2"></i>{{ form.uuid.label.text }}
                                    </label>
                                    {{ form.uuid(class="form-control") }}
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb"></i>
                                        稿件首次送审后Elsevier发送的Track链接或其中的UUID
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-clock me-2"></i>{{ form.interval.label.text }}
                                    </label>
                                    {{ form.interval(class="form-control") }}
                                    <div class="form-text">
                                        <i class="fas fa-chart-line"></i>
                                        推荐30分钟，平衡及时性与服务器负载
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-rocket"></i>
                                    <strong>智能监控特性：</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>🔍 自动检测状态变化、更新时间变化</li>
                                        <li>👥 智能识别审稿人信息变化</li>
                                        <li>✅ 自动检测RRC（Reviews Received）状态</li>
                                        <li>📧 美化HTML邮件通知</li>
                                        <li>📊 详细的时间统计分析</li>
                                    </ul>
                                </div>

                                <button type="submit" class="btn btn-primary w-100"
                                        {% if not can_submit %}disabled{% endif %}
                                        id="submitBtn">
                                    <i class="fas fa-rocket me-2"></i>
                                    {% if can_submit %}启动智能监控{% else %}已达最大任务数{% endif %}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="col-lg-6">
                    <div class="tasks-section">
                        <h3 class="mb-4">
                            <i class="fas fa-list-check me-2" style="color: var(--primary-color);"></i>
                            监控任务 ({{ monitor_tasks|length }}/{{ stats.max_tasks }})
                        </h3>

                        <div class="task-list" style="max-height: 600px; overflow-y: auto;">
                            {% if monitor_tasks %}
                                {% for task_id, task in monitor_tasks.items() %}
                                    <div class="task-item">
                                        <div class="task-header">
                                            <div class="task-info">
                                                <h5>
                                                    <i class="fas fa-satellite-dish"></i>
                                                    监控任务 #{{ loop.index }}
                                                    <span class="task-status-badge {% if task.get('fail_count', 0) == 0 %}task-status-normal{% else %}task-status-warning{% endif %}">
                                                        {% if task.get('fail_count', 0) == 0 %}
                                                            <i class="fas fa-check-circle"></i>运行中
                                                        {% else %}
                                                            <i class="fas fa-exclamation-triangle"></i>异常
                                                        {% endif %}
                                                    </span>
                                                </h5>
                                            </div>

                                            {% if admin %}
                                                <div class="task-actions">
                                                    <button class="btn btn-outline-primary btn-sm"
                                                            onclick="window.open('{{ url_for('view_logs', masked_email=mask_email(task.recipient_email), uuid=task.uuid) }}', '_blank')"
                                                            title="查看日志">
                                                        <i class="fas fa-chart-line"></i>
                                                    </button>
                                                    <form method="post" action="{{ url_for('delete_task', task_id=task_id) }}"
                                                          style="display: inline;"
                                                          onsubmit="return showDeleteConfirm('{{ loop.index }}')">
                                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="删除任务">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <div class="task-meta">
                                            <div class="task-meta-item">
                                                <i class="fas fa-user-circle"></i>
                                                <span>{{ mask_email(task.recipient_email) }}</span>
                                            </div>
                                            <div class="task-meta-item">
                                                <i class="fas fa-stopwatch"></i>
                                                <span>每 {{ task.interval }} 分钟</span>
                                            </div>
                                            <div class="task-meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                <span>{{ task.created_at.strftime('%m-%d %H:%M') if task.get('created_at') else '未知时间' }}</span>
                                            </div>
                                            <div class="task-meta-item">
                                                <i class="fas fa-{% if task.get('fail_count', 0) == 0 %}shield-alt{% else %}exclamation-circle{% endif %}"></i>
                                                <span>{% if task.get('fail_count', 0) == 0 %}运行正常{% else %}失败 {{ task.fail_count }} 次{% endif %}</span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <div class="empty-icon-container">
                                        <div class="empty-icon">
                                            <i class="fas fa-satellite-dish"></i>
                                        </div>
                                        <div class="empty-particles">
                                            <div class="particle"></div>
                                            <div class="particle"></div>
                                            <div class="particle"></div>
                                        </div>
                                    </div>
                                    <h5 class="empty-title">暂无监控任务</h5>
                                    <p class="empty-subtitle">系统准备就绪，等待您的第一个监控任务</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导航按钮 -->
            <div class="nav-buttons">
                <a href="{{ url_for('query_manuscript') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>稿件状态查询
                </a>

                {% if admin %}
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-2"></i>管理面板
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>退出管理
                    </a>
                {% else %}
                    <a href="{{ url_for('admin_login') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-shield me-2"></i>管理员登录
                    </a>
                {% endif %}


            </div>

            <!-- 页脚 -->
            <div style="background: #f8fafc; padding: 2rem; text-align: center; color: #64748b;">
                <p class="mb-2">
                    <i class="fas fa-heart text-danger me-1"></i>
                    由 <strong>linwu</strong> 精心打造的极致版监控平台
                </p>
                <p class="mb-0">
                    <small>
                        <i class="fas fa-code me-1"></i>
                        基于 Flask + Bootstrap 5 + 现代化设计语言
                        <span class="mx-2">|</span>
                        <i class="fas fa-clock me-1"></i>
                        最后检查: {{ stats.last_check }}
                    </small>
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单提交增强
        document.getElementById('monitorForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // 防止重复提交
            if (submitBtn.disabled) {
                e.preventDefault();
                return false;
            }

            // 前端表单验证
            const senderEmail = this.querySelector('input[name="sender_email"]').value.trim();
            const password = this.querySelector('input[name="password"]').value.trim();
            const recipientEmail = this.querySelector('input[name="recipient_email"]').value.trim();
            const uuid = this.querySelector('input[name="uuid"]').value.trim();

            // 基础验证
            if (!senderEmail || !password || !recipientEmail || !uuid) {
                showMobileAlert('❌ 请填写所有必填字段');
                return false;
            }

            // QQ邮箱验证
            if (!senderEmail.endsWith('@qq.com')) {
                showMobileAlert('❌ 发件人邮箱必须是QQ邮箱（@qq.com）');
                return false;
            }

            // 授权码长度验证
            if (password.length < 10) {
                showMobileAlert('❌ QQ邮箱授权码长度应为16位字符');
                return false;
            }

            // UUID长度验证
            if (uuid.length < 10) {
                showMobileAlert('❌ 请输入完整的UUID或Track链接');
                return false;
            }

            // 设置加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在启动监控...';

            // 移动端触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // 10秒后如果还没有响应，恢复按钮状态
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                    showMobileAlert('⚠️ 请求超时，请检查网络连接后重试');
                }
            }, 10000);
        });

        // 移动端友好的提示框
        function showMobileAlert(message) {
            if (window.innerWidth <= 768) {
                // 创建移动端友好的提示
                const alertDiv = document.createElement('div');
                alertDiv.className = 'mobile-alert';
                alertDiv.innerHTML = \`
                    <div class="mobile-alert-content">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>\${message}</span>
                    </div>
                \`;

                // 添加样式
                if (!document.getElementById('mobile-alert-styles')) {
                    const styles = document.createElement('style');
                    styles.id = 'mobile-alert-styles';
                    styles.textContent = \`
                        .mobile-alert {
                            position: fixed;
                            top: 20px;
                            left: 50%;
                            transform: translateX(-50%);
                            z-index: 10001;
                            animation: slideDown 0.3s ease;
                        }

                        .mobile-alert-content {
                            background: #fee2e2;
                            color: #dc2626;
                            padding: 1rem 1.5rem;
                            border-radius: 12px;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                            display: flex;
                            align-items: center;
                            gap: 0.75rem;
                            max-width: 90vw;
                            font-size: 0.9rem;
                            font-weight: 500;
                            border: 1px solid #fecaca;
                        }

                        @keyframes slideDown {
                            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                            to { transform: translateX(-50%) translateY(0); opacity: 1; }
                        }
                    \`;
                    document.head.appendChild(styles);
                }

                document.body.appendChild(alertDiv);

                // 3秒后自动移除
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);

                // 触觉反馈
                if (navigator.vibrate) {
                    navigator.vibrate([100, 50, 100]);
                }
            } else {
                alert(message);
            }
        }

        // 实时统计更新
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.querySelector('.stat-value').textContent = data.active_tasks + '/' + data.max_tasks;
                    document.querySelectorAll('.stat-value')[1].textContent = data.total_checks;
                    document.querySelectorAll('.stat-value')[2].textContent = data.total_notifications;
                })
                .catch(error => console.log('Stats update failed:', error));
        }

        // 每30秒更新一次统计
        setInterval(updateStats, 30000);

        // 美化的删除确认对话框
        function showDeleteConfirm(taskNumber) {
            return new Promise((resolve) => {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'delete-modal-overlay';
                modal.innerHTML = `
                    <div class="delete-modal">
                        <div class="delete-modal-header">
                            <div class="delete-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h4>确认删除</h4>
                        </div>
                        <div class="delete-modal-body">
                            <p>您确定要删除监控任务 #${taskNumber} 吗？</p>
                            <p class="delete-warning">此操作将同时删除相关日志，且无法恢复！</p>
                        </div>
                        <div class="delete-modal-footer">
                            <button class="btn-cancel" onclick="closeDeleteModal(false)">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button class="btn-confirm" onclick="closeDeleteModal(true)">
                                <i class="fas fa-trash me-1"></i>确认删除
                            </button>
                        </div>
                    </div>
                `;

                // 添加样式
                if (!document.getElementById('delete-modal-styles')) {
                    const styles = document.createElement('style');
                    styles.id = 'delete-modal-styles';
                    styles.textContent = \`
                        .delete-modal-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.5);
                            backdrop-filter: blur(5px);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 10000;
                            animation: fadeIn 0.3s ease;
                        }

                        .delete-modal {
                            background: white;
                            border-radius: 16px;
                            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                            max-width: 400px;
                            width: 90%;
                            animation: slideIn 0.3s ease;
                        }

                        .delete-modal-header {
                            padding: 1.5rem;
                            text-align: center;
                            border-bottom: 1px solid #e5e7eb;
                        }

                        .delete-icon {
                            width: 60px;
                            height: 60px;
                            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 1rem;
                            color: #dc2626;
                            font-size: 1.5rem;
                        }

                        .delete-modal-header h4 {
                            margin: 0;
                            color: #1f2937;
                            font-weight: 600;
                        }

                        .delete-modal-body {
                            padding: 1.5rem;
                            text-align: center;
                        }

                        .delete-modal-body p {
                            margin: 0 0 0.5rem 0;
                            color: #4b5563;
                        }

                        .delete-warning {
                            color: #dc2626;
                            font-size: 0.9rem;
                            font-weight: 500;
                        }

                        .delete-modal-footer {
                            padding: 1rem 1.5rem 1.5rem;
                            display: flex;
                            gap: 0.75rem;
                            justify-content: center;
                        }

                        .btn-cancel, .btn-confirm {
                            padding: 0.75rem 1.5rem;
                            border: none;
                            border-radius: 8px;
                            font-weight: 500;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                        }

                        .btn-cancel {
                            background: #f3f4f6;
                            color: #4b5563;
                        }

                        .btn-cancel:hover {
                            background: #e5e7eb;
                        }

                        .btn-confirm {
                            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                            color: white;
                        }

                        .btn-confirm:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
                        }

                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }

                        @keyframes slideIn {
                            from { transform: translateY(-20px) scale(0.95); opacity: 0; }
                            to { transform: translateY(0) scale(1); opacity: 1; }
                        }

                        /* 移动端对话框优化 */
                        @media (max-width: 768px) {
                            .delete-modal {
                                width: 95%;
                                max-width: 350px;
                                margin: 1rem;
                            }

                            .delete-modal-header {
                                padding: 1.25rem;
                            }

                            .delete-icon {
                                width: 50px;
                                height: 50px;
                                font-size: 1.25rem;
                            }

                            .delete-modal-body {
                                padding: 1.25rem;
                            }

                            .delete-modal-footer {
                                padding: 1rem 1.25rem 1.25rem;
                                flex-direction: column;
                                gap: 0.75rem;
                            }

                            .btn-cancel, .btn-confirm {
                                width: 100%;
                                padding: 0.875rem 1.5rem;
                                min-height: 48px;
                                justify-content: center;
                            }
                        }
                    \`;
                    document.head.appendChild(styles);
                }

                document.body.appendChild(modal);

                // 全局函数
                window.closeDeleteModal = function(confirmed) {
                    modal.remove();
                    resolve(confirmed);
                };
            });
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .task-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
'''

# 管理员登录模板
ADMIN_LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 3rem;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .form-control {
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.875rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .back-link {
            text-align: center;
            margin-top: 2rem;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <h2 class="mb-2">管理员登录</h2>
            <p class="text-muted">访问系统管理功能</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} mb-3">
                        <i class="fas fa-{% if category == 'danger' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="post">
            {{ form.hidden_tag() }}

            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-user me-2"></i>{{ form.username.label.text }}
                </label>
                {{ form.username(class="form-control", placeholder="请输入管理员账号") }}
            </div>

            <div class="mb-4">
                <label class="form-label">
                    <i class="fas fa-lock me-2"></i>{{ form.password.label.text }}
                </label>
                {{ form.password(class="form-control", placeholder="请输入密码") }}
            </div>

            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt me-2"></i>登录
            </button>
        </form>

        <div class="back-link">
            <a href="{{ url_for('index') }}">
                <i class="fas fa-arrow-left me-1"></i>返回主页
            </a>
        </div>
    </div>
</body>
</html>
'''

# 管理员仪表板模板
ADMIN_DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表板 - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1e293b;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .tasks-section {
            padding: 2rem;
        }

        .task-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .task-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            font-size: 0.9rem;
            color: #64748b;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
        }

        .admin-actions {
            padding: 2rem;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="dashboard-container">
            <div class="dashboard-header">
                <h1><i class="fas fa-tachometer-alt me-3"></i>管理员仪表板</h1>
                <p>系统监控与任务管理中心</p>
            </div>

            <!-- 系统统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-value h3 mb-1">{{ stats.task_stats.total }}</div>
                    <div class="stat-label text-muted">总任务数</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value h3 mb-1">{{ stats.task_stats.active }}</div>
                    <div class="stat-label text-muted">活跃任务</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value h3 mb-1">{{ stats.task_stats.failed }}</div>
                    <div class="stat-label text-muted">失败任务</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="stat-value h3 mb-1">{{ stats.total_notifications }}</div>
                    <div class="stat-label text-muted">发送通知</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="stat-value h3 mb-1">{{ stats.total_checks }}</div>
                    <div class="stat-label text-muted">检查次数</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value h4 mb-1">{{ stats.uptime }}</div>
                    <div class="stat-label text-muted">运行时间</div>
                </div>
            </div>

            <!-- 任务管理 -->
            <div class="tasks-section">
                <h3 class="mb-4">
                    <i class="fas fa-list-check me-2"></i>
                    任务管理 ({{ monitor_tasks|length }}/{{ max_tasks }})
                </h3>

                {% if monitor_tasks %}
                    {% for task_id, task in monitor_tasks.items() %}
                        <div class="task-card">
                            <div class="task-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    任务 {{ task_id }}
                                </h5>
                                <div class="d-flex gap-2">
                                    <a href="{{ url_for('view_logs', masked_email=mask_email(task.recipient_email), uuid=task.uuid) }}"
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> 查看日志
                                    </a>
                                    <form method="post" action="{{ url_for('delete_task', task_id=task_id) }}"
                                          style="display: inline;"
                                          onsubmit="return showDeleteConfirm('{{ task_id }}')">
                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <div class="task-meta">
                                <div><i class="fas fa-envelope me-2"></i>收件人: {{ mask_email(task.recipient_email) }}</div>
                                <div><i class="fas fa-clock me-2"></i>间隔: {{ task.interval }} 分钟</div>
                                <div><i class="fas fa-calendar me-2"></i>创建: {{ task.created_at.strftime('%Y-%m-%d %H:%M') if task.get('created_at') else '未知' }}</div>
                                <div>
                                    <i class="fas fa-{% if task.get('fail_count', 0) == 0 %}check-circle text-success{% else %}exclamation-triangle text-warning{% endif %} me-2"></i>
                                    状态: {% if task.get('fail_count', 0) == 0 %}正常运行{% else %}失败 {{ task.fail_count }} 次{% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无监控任务</h5>
                    </div>
                {% endif %}
            </div>

            <!-- 管理操作 -->
            <div class="admin-actions">
                <a href="{{ url_for('admin_config') }}" class="btn btn-primary">
                    <i class="fas fa-cog me-2"></i>系统配置
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>返回主页
                </a>
                <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                </a>
            </div>

            <div class="text-center py-3" style="background: #f8fafc; color: #64748b;">
                <small>
                    <i class="fas fa-clock me-1"></i>
                    最后检查: {{ stats.last_check }}
                    <span class="mx-2">|</span>
                    <i class="fas fa-server me-1"></i>
                    系统运行时间: {{ stats.uptime }}
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# 系统配置模板
ADMIN_CONFIG_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .config-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
        }

        .config-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .config-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .form-control {
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.875rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .back-links {
            text-align: center;
            margin-top: 2rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .back-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <div class="config-header">
            <div class="config-icon">
                <i class="fas fa-cog"></i>
            </div>
            <h2 class="mb-2">系统配置</h2>
            <p class="text-muted">调整系统运行参数</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} mb-3">
                        <i class="fas fa-check-circle me-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="post">
            {{ form.hidden_tag() }}

            <div class="mb-3">
                <label class="form-label">
                    <i class="fas fa-tasks me-2"></i>{{ form.max_tasks.label.text }}
                </label>
                {{ form.max_tasks(class="form-control") }}
                <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    系统同时支持的最大监控任务数量
                </div>
            </div>

            <div class="mb-4">
                <label class="form-label">
                    <i class="fas fa-file-alt me-2"></i>{{ form.max_log_lines.label.text }}
                </label>
                {{ form.max_log_lines(class="form-control") }}
                <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    每个任务日志文件保留的最大行数
                </div>
            </div>

            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-save me-2"></i>保存配置
            </button>
        </form>

        <div class="back-links">
            <a href="{{ url_for('admin_dashboard') }}">
                <i class="fas fa-arrow-left me-1"></i>返回仪表板
            </a>
            <span class="text-muted">|</span>
            <a href="{{ url_for('index') }}">
                <i class="fas fa-home me-1"></i>返回主页
            </a>
        </div>
    </div>
</body>
</html>
'''

# 日志查看模板
ADMIN_LOGS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务日志 - {{ masked_email }} - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1e293b;
        }

        .logs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 1000px;
            overflow: hidden;
        }

        .logs-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .logs-content {
            padding: 2rem;
        }

        .log-entry {
            background: white;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.75rem;
            border-left: 4px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .log-entry.log-info { border-left-color: #3b82f6; }
        .log-entry.log-notification { border-left-color: #10b981; }
        .log-entry.log-warning { border-left-color: #f59e0b; }
        .log-entry.log-error { border-left-color: #ef4444; }
        .log-entry.log-check { border-left-color: #8b5cf6; }
        .log-entry.log-admin { border-left-color: #06b6d4; }

        .log-timestamp {
            font-size: 0.85rem;
            color: #64748b;
            font-weight: 500;
        }

        .log-message {
            margin-top: 0.5rem;
            line-height: 1.5;
        }

        .log-type-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 0.5rem;
        }

        .log-type-info { background: #dbeafe; color: #1e40af; }
        .log-type-notification { background: #d1fae5; color: #065f46; }
        .log-type-warning { background: #fef3c7; color: #92400e; }
        .log-type-error { background: #fee2e2; color: #991b1b; }
        .log-type-check { background: #ede9fe; color: #5b21b6; }
        .log-type-admin { background: #cffafe; color: #155e75; }

        .actions-bar {
            padding: 1.5rem 2rem;
            background: #f8fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .empty-logs {
            text-align: center;
            padding: 3rem;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="logs-container">
            <div class="logs-header">
                <h1><i class="fas fa-file-alt me-3"></i>任务日志</h1>
                <p>用户: {{ masked_email }} | UUID: {{ uuid[:8] }}...</p>
            </div>

            <div class="logs-content">
                {% if logs %}
                    {% for log in logs|reverse %}
                        <div class="log-entry log-{{ log.type }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="log-timestamp">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ log.timestamp[:19].replace('T', ' ') }}
                                    <span class="log-type-badge log-type-{{ log.type }}">{{ log.type }}</span>
                                </div>
                            </div>
                            <div class="log-message">{{ log.message }}</div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-logs">
                        <i class="fas fa-inbox fa-4x mb-3"></i>
                        <h5>暂无日志记录</h5>
                        <p class="text-muted">该任务尚未生成任何日志</p>
                    </div>
                {% endif %}
            </div>

            <div class="actions-bar">
                <div>
                    <span class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        共 {{ logs|length }} 条日志记录
                    </span>
                </div>
                <div class="d-flex gap-2">
                    <form method="post" action="{{ url_for('clear_logs', masked_email=masked_email, uuid=uuid) }}"
                          style="display: inline;"
                          onsubmit="return confirm('确认清空所有日志吗？此操作不可恢复！')">
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>清空日志
                        </button>
                    </form>
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>返回仪表板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动刷新日志
        function refreshLogs() {
            location.reload();
        }

        // 每30秒自动刷新
        setInterval(refreshLogs, 30000);

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const entries = document.querySelectorAll('.log-entry');
            entries.forEach((entry, index) => {
                entry.style.opacity = '0';
                entry.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    entry.style.transition = 'all 0.5s ease';
                    entry.style.opacity = '1';
                    entry.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });
    </script>
</body>
</html>
'''

# 查询输入模板
QUERY_INPUT_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稿件状态查询 - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .query-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .query-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .query-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2.5rem;
        }

        .form-control {
            padding: 1rem 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
        }

        .info-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #0ea5e9;
        }

        .example-section {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #22c55e;
        }

        .back-links {
            text-align: center;
            margin-top: 2rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .back-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-links a:hover {
            background: rgba(102, 126, 234, 0.1);
            text-decoration: none;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .feature-list i {
            width: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="query-container">
        <div class="query-header">
            <div class="query-icon">
                <i class="fas fa-search"></i>
            </div>
            <h1 class="mb-2">稿件状态查询</h1>
            <p class="text-muted">实时查询您的稿件审稿进度</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="post">
            <div class="mb-4">
                <label class="form-label h5">
                    <i class="fas fa-link me-2"></i>请输入稿件UUID或Track链接
                </label>
                <input type="text" name="uuid" class="form-control"
                       placeholder="例：81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx 或完整Track链接"
                       required>
                <div class="form-text mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    支持完整Track链接或纯UUID格式
                </div>
            </div>

            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-search me-2"></i>立即查询
            </button>
        </form>

        <div class="info-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>查询功能特色
            </h6>
            <ul class="feature-list">
                <li><i class="fas fa-chart-line text-primary"></i>详细的审稿进度分析</li>
                <li><i class="fas fa-clock text-success"></i>精确的时间统计计算</li>
                <li><i class="fas fa-users text-info"></i>审稿人状态实时跟踪</li>
                <li><i class="fas fa-trophy text-warning"></i>智能状态识别系统</li>
                <li><i class="fas fa-mobile-alt text-secondary"></i>完美的移动端适配</li>
            </ul>
        </div>

        <div class="example-section">
            <h6 class="mb-3">
                <i class="fas fa-question-circle me-2"></i>如何获取UUID？
            </h6>
            <p class="mb-2">UUID通常在以下情况下获得：</p>
            <ul class="feature-list">
                <li><i class="fas fa-envelope text-primary"></i>稿件首次送审后的系统邮件</li>
                <li><i class="fas fa-link text-success"></i>Track链接中的uuid参数</li>
                <li><i class="fas fa-globe text-info"></i>期刊官网的稿件跟踪页面</li>
            </ul>
        </div>

        <div class="back-links">
            <a href="/">
                <i class="fas fa-home me-1"></i>返回监控主页
            </a>
            <span class="text-muted">|</span>
            <a href="/admin/login">
                <i class="fas fa-user-shield me-1"></i>管理员登录
            </a>
        </div>

        <div class="text-center mt-4 pt-3" style="border-top: 1px solid #e2e8f0;">
            <small class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                安全查询 | 实时数据 | 专业服务
            </small>
        </div>
    </div>

    <script>
        // 表单增强
        document.querySelector('form').addEventListener('submit', function(e) {
            const btn = this.querySelector('button[type="submit"]');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在查询...';
            btn.disabled = true;
        });

        // 输入框增强
        const input = document.querySelector('input[name="uuid"]');
        input.addEventListener('paste', function(e) {
            setTimeout(() => {
                const value = this.value.trim();
                if (value.includes('uuid=')) {
                    this.style.borderColor = '#22c55e';
                } else if (value.length > 30) {
                    this.style.borderColor = '#3b82f6';
                }
            }, 100);
        });
    </script>
</body>
</html>
'''

# 查询结果模板
QUERY_RESULT_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 稿件状态详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1e293b;
            padding: 1rem 0;
        }

        .result-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            margin: 1rem auto;
            max-width: 1200px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .result-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .result-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .manuscript-info {
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .info-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: white;
            flex-shrink: 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 1.2rem;
            color: white;
            margin: 1rem 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .uuid-display {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
            font-size: 0.85rem;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            border: 1px solid #cbd5e1;
            word-break: break-all;
            letter-spacing: 0.5px;
            color: #475569;
            font-weight: 500;
        }

        .status-info-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .status-info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #10b981 100%);
        }

        .status-description {
            color: #475569;
            font-size: 0.95rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .status-timestamp {
            color: #64748b;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
        }

        .reviewers-section {
            padding: 2rem;
        }

        .reviewer-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .reviewer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .reviewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .reviewer-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            font-size: 0.9rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.25rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .actions-section {
            padding: 2rem;
            background: #f8fafc;
            text-align: center;
        }

        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #64748b;
            color: #64748b;
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: #64748b;
            color: white;
            transform: translateY(-2px);
        }

        .round-stats {
            border-radius: 12px;
            padding: 1.25rem;
            margin: 1.5rem 0;
            border-left: 4px solid;
            position: relative;
            transition: all 0.3s ease;
        }

        .round-stats:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 第一轮 - 蓝色系 */
        .round-stats.round-1 {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left-color: #3b82f6;
        }

        /* 第二轮 - 绿色系 */
        .round-stats.round-2 {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-left-color: #10b981;
        }

        /* 第三轮 - 橙色系 */
        .round-stats.round-3 {
            background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
            border-left-color: #f97316;
        }

        /* 第四轮 - 紫色系 */
        .round-stats.round-4 {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border-left-color: #8b5cf6;
        }

        /* 第五轮及以上 - 红色系 */
        .round-stats.round-5-plus {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border-left-color: #ef4444;
        }

        .round-stats h5 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .round-stats .round-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: auto;
        }

        .round-1 .round-badge {
            background: #3b82f6;
            color: white;
        }

        .round-2 .round-badge {
            background: #10b981;
            color: white;
        }

        .round-3 .round-badge {
            background: #f97316;
            color: white;
        }

        .round-4 .round-badge {
            background: #8b5cf6;
            color: white;
        }

        .round-5-plus .round-badge {
            background: #ef4444;
            color: white;
        }

        @media (max-width: 768px) {
            .result-container {
                margin: 0.5rem;
                border-radius: 16px;
            }

            .result-header,
            .manuscript-info,
            .reviewers-section,
            .actions-section {
                padding: 1rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .reviewer-meta {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="result-container">
            <!-- 头部 -->
            <div class="result-header">
                <h1 class="mb-2" style="position: relative; z-index: 1;">
                    <i class="fas fa-file-alt me-3"></i>稿件状态详情
                </h1>
                <p class="mb-0" style="position: relative; z-index: 1; opacity: 0.9;">
                    实时查询结果 | {{ query_time }}
                </p>
            </div>

            <!-- 稿件基本信息 -->
            <div class="manuscript-info">
                <div class="info-card">
                    <h3 class="mb-3">
                        <i class="fas fa-info-circle me-2" style="color: #667eea;"></i>
                        稿件基本信息
                    </h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                                <i class="fas fa-file-text"></i>
                            </div>
                            <div>
                                <div class="fw-bold">稿件标题</div>
                                <div class="text-muted">{{ title or '未知' }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div>
                                <div class="fw-bold">稿件编号</div>
                                <div class="text-muted">{{ pubd_number or '未知' }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                                <i class="fas fa-book"></i>
                            </div>
                            <div>
                                <div class="fw-bold">投稿期刊</div>
                                <div class="text-muted">{{ journal or '未知' }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <div class="fw-bold">第一作者</div>
                                <div class="text-muted">{{ first_author or '未知' }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="fw-bold">通讯作者</div>
                                <div class="text-muted">{{ corresponding_author or '未知' }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <div>
                                <div class="fw-bold">UUID</div>
                                <div class="text-muted uuid-display">{{ uuid }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态信息 -->
                <div class="info-card status-info-card">
                    <h3 class="mb-4">
                        <i class="fas fa-chart-line me-2" style="color: #667eea;"></i>
                        当前状态与时间统计
                    </h3>

                    <div class="text-center mb-4">
                        <div class="status-badge" style="background: linear-gradient(135deg,
                            {% if status_info.color == 'success' %}#10b981, #059669
                            {% elif status_info.color == 'warning' %}#f59e0b, #d97706
                            {% elif status_info.color == 'danger' %}#ef4444, #dc2626
                            {% elif status_info.color == 'info' %}#3b82f6, #1d4ed8
                            {% elif status_info.color == 'primary' %}#667eea, #764ba2
                            {% else %}#6b7280, #4b5563{% endif %});">
                            <i class="{{ status_info.icon }}"></i>
                            {{ status_info.text }}
                        </div>
                        <div class="mt-3">
                            <div class="status-description">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>当前稿件状态</strong> - 数据实时更新
                            </div>
                            <div class="status-timestamp">
                                <i class="fas fa-clock me-1"></i>
                                查询时间: {{ query_time }}
                            </div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value text-primary">{{ submission_stats.days }}</div>
                            <div class="stat-label">投稿天数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value text-success">{{ invited_count }}</div>
                            <div class="stat-label">邀请审稿人</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value text-warning">{{ accepted_count }}</div>
                            <div class="stat-label">接受审稿</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value text-info">{{ current_round }}</div>
                            <div class="stat-label">当前轮次</div>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div>
                                <div class="fw-bold">投稿时间</div>
                                <div class="text-muted">{{ submitted }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <div class="fw-bold">最近更新</div>
                                <div class="text-muted">{{ updated }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div>
                                <div class="fw-bold">总耗时</div>
                                <div class="text-muted">{{ submission_stats.formatted }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon" style="background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);">
                                <i class="fas fa-stopwatch"></i>
                            </div>
                            <div>
                                <div class="fw-bold">距离更新</div>
                                <div class="text-muted">{{ last_update_stats.formatted }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审稿人详情 -->
            {% if reviewers %}
            <div class="reviewers-section">
                <h3 class="mb-4">
                    <i class="fas fa-users me-2" style="color: #667eea;"></i>
                    审稿人详细信息
                </h3>

                <!-- 轮次统计 -->
                {% if group_stats %}
                    {% for revision, stats in group_stats.items() %}
                        <div class="round-stats {% if revision + 1 == 1 %}round-1{% elif revision + 1 == 2 %}round-2{% elif revision + 1 == 3 %}round-3{% elif revision + 1 == 4 %}round-4{% else %}round-5-plus{% endif %}">
                            <h5>
                                <i class="fas fa-layer-group me-2"></i>
                                第 {{ revision + 1 }} 轮审稿
                                <span class="round-badge">
                                    <i class="fas fa-circle"></i>
                                    Round {{ revision + 1 }}
                                </span>
                            </h5>
                            <div class="d-flex gap-3 flex-wrap">
                                <span><i class="fas fa-paper-plane me-1" style="color: #3b82f6;"></i><strong>邀请:</strong> {{ stats.invited }} 人</span>
                                <span><i class="fas fa-handshake me-1" style="color: #10b981;"></i><strong>接受:</strong> {{ stats.accepted }} 人</span>
                                <span><i class="fas fa-flag-checkered me-1" style="color: #f59e0b;"></i><strong>完成:</strong> {{ stats.completed }} 人</span>
                                <span><i class="fas fa-chart-pie me-1" style="color: #8b5cf6;"></i><strong>完成率:</strong> {{ "%.1f"|format((stats.completed / stats.invited * 100) if stats.invited > 0 else 0) }}%</span>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- 审稿人列表 -->
                {% for reviewer in reviewers %}
                    <div class="reviewer-card">
                        <div class="reviewer-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-graduate me-2 text-primary"></i>
                                审稿人 #{{ reviewer.id }}
                                <span class="badge ms-2 {% if reviewer.revision + 1 == 1 %}bg-primary{% elif reviewer.revision + 1 == 2 %}bg-success{% elif reviewer.revision + 1 == 3 %}bg-warning{% elif reviewer.revision + 1 == 4 %}text-bg-secondary{% else %}bg-danger{% endif %}">
                                    第{{ reviewer.revision + 1 }}轮
                                </span>
                            </h5>
                            <div class="text-muted">
                                {% if reviewer.complete_time != "未完成" %}
                                    <i class="fas fa-check-circle text-success"></i> 已完成
                                {% else %}
                                    <i class="fas fa-clock text-warning"></i> 进行中
                                {% endif %}
                            </div>
                        </div>

                        <div class="reviewer-meta">
                            <div class="meta-item">
                                <i class="fas fa-paper-plane text-primary"></i>
                                <span>邀请时间: {{ reviewer.invite_time }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-handshake text-success"></i>
                                <span>接受时间: {{ reviewer.accept_time }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-flag-checkered text-warning"></i>
                                <span>完成时间: {{ reviewer.complete_time }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-hourglass-start text-info"></i>
                                <span>接受耗时: {{ reviewer.accept_duration }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-hourglass-end text-purple"></i>
                                <span>审稿耗时: {{ reviewer.review_duration }}</span>
                            </div>
                            {% if reviewer.current_duration %}
                                <div class="meta-item">
                                    <i class="fas fa-clock text-danger"></i>
                                    <span>当前耗时: {{ reviewer.current_duration }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 操作按钮 -->
            <div class="actions-section">
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ url_for('query_manuscript') }}" class="btn btn-primary">
                        <i class="fas fa-redo me-2"></i>重新查询
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>返回监控平台
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-secondary">
                        <i class="fas fa-print me-2"></i>打印报告
                    </button>
                </div>

                <div class="mt-4 pt-3" style="border-top: 1px solid #e2e8f0;">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        数据实时更新 | 北京时间 | 查询时间: {{ query_time }}
                    </small>
                </div>
            </div>

            <!-- 页脚 -->
            <div class="text-center py-3" style="background: #f8fafc; color: #64748b;">
                <small>
                    <i class="fas fa-heart text-danger me-1"></i>
                    © 2025 Elsevier 稿件监控平台 - 极致版 | 由 <strong>linwu</strong> 精心打造
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.info-card, .reviewer-card, .stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 打印样式优化
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
            document.querySelector('.result-container').style.boxShadow = 'none';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.background = '';
            document.querySelector('.result-container').style.boxShadow = '';
        });
    </script>
</body>
</html>
'''

# 查询错误模板
QUERY_ERROR_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询失败 - Elsevier 监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 600px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 3rem;
        }

        .error-title {
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #64748b;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .help-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #f59e0b;
            text-align: left;
        }

        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 0.875rem 2rem;
            transition: all 0.3s ease;
            border: none;
            margin: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-secondary {
            border: 2px solid #64748b;
            color: #64748b;
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: #64748b;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <h1 class="error-title">查询失败</h1>

        <div class="error-message">
            <p>很抱歉，无法查询到您输入的稿件信息。</p>
            <p>这可能是由于以下原因造成的：</p>
        </div>

        <div class="help-section">
            <h6 class="mb-3">
                <i class="fas fa-lightbulb me-2"></i>常见问题与解决方案
            </h6>
            <ul class="text-start">
                <li><strong>UUID格式错误：</strong>请确保输入的是完整的36位UUID</li>
                <li><strong>稿件尚未送审：</strong>只有送审后的稿件才能查询状态</li>
                <li><strong>网络连接问题：</strong>请检查网络连接后重试</li>
                <li><strong>服务器暂时不可用：</strong>请稍后再试</li>
                <li><strong>UUID已过期：</strong>某些旧稿件的UUID可能已失效</li>
            </ul>
        </div>

        <div class="help-section" style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); border-left-color: #3b82f6;">
            <h6 class="mb-3">
                <i class="fas fa-question-circle me-2"></i>如何获取正确的UUID？
            </h6>
            <ul class="text-start">
                <li>查看稿件首次送审后收到的系统邮件</li>
                <li>从Track链接中提取uuid参数</li>
                <li>联系期刊编辑部获取稿件跟踪信息</li>
                <li>确认稿件编号与UUID的对应关系</li>
            </ul>
        </div>

        <div class="mt-4">
            <a href="{{ url_for('query_manuscript') }}" class="btn btn-primary">
                <i class="fas fa-redo me-2"></i>重新查询
            </a>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-home me-2"></i>返回主页
            </a>
        </div>

        <div class="mt-4 pt-3" style="border-top: 1px solid #e2e8f0;">
            <small class="text-muted">
                <i class="fas fa-headset me-1"></i>
                如需帮助，请联系技术支持或查看使用说明
            </small>
        </div>
    </div>
</body>
</html>
'''

# 启动应用
if __name__ == '__main__':
    logger.info("🚀 启动 Elsevier 稿件监控平台 - 极致版")
    logger.info(f"📊 配置信息 - 最大任务数: {MAX_TASKS}, 最大日志行数: {MAX_LOG_LINES}")
    logger.info(f"🔧 管理员账号: {ADMIN_USERNAME}")

    app.run(host='0.0.0.0', port=2001, debug=False, threaded=True)
