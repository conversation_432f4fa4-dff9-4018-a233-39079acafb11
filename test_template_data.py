#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板数据传递
"""

# 模拟新格式数据
new_format_data = {
    "Uuid": "016cff7e-51d9-43de-958e-677c0e61dc1d",
    "CorrespondingAuthor": "<PERSON><PERSON>",
    "DocumentId": 91824,
    "FirstAuthor": "<PERSON><PERSON><PERSON>",
    "JournalAcronym": "HMT",
    "JournalName": "International Journal of Heat and Mass Transfer",
    "LastUpdated": 1754744192,
    "LatestRevisionNumber": 0,
    "ManuscriptTitle": "An Improved Numerical Model for Ice Crystal Sticking under Mixed-Phase Icing Condition",
    "PubdNumber": "HMT-D-25-03540",
    "Status": 3,
    "SubmissionDate": 1750627742,
    "ReviewSummary": {
        "ReviewsCompleted": "1",
        "ReviewInvitationsAccepted": "2",
        "ReviewInvitationsSent": "2+"
    }
}

def test_data_processing():
    """测试数据处理逻辑"""
    print("=== 测试模板数据处理 ===")
    
    review_summary = new_format_data.get("ReviewSummary", {})
    invited_count = review_summary.get("ReviewInvitationsSent", "0")
    accepted_count = review_summary.get("ReviewInvitationsAccepted", "0")
    completed_count = review_summary.get("ReviewsCompleted", "0")
    
    print(f"原始数据:")
    print(f"  invited_count: {invited_count} (类型: {type(invited_count)})")
    print(f"  accepted_count: {accepted_count} (类型: {type(accepted_count)})")
    print(f"  completed_count: {completed_count} (类型: {type(completed_count)})")
    
    # 解析数字
    try:
        invited_num = int(invited_count.replace('+', '')) if isinstance(invited_count, str) else int(invited_count)
        accepted_num = int(accepted_count) if isinstance(accepted_count, str) else int(accepted_count)
        completed_num = int(completed_count) if isinstance(completed_count, str) else int(completed_count)
        
        print(f"\n解析后的数字:")
        print(f"  invited_num: {invited_num}")
        print(f"  accepted_num: {accepted_num}")
        print(f"  completed_num: {completed_num}")
        
    except (ValueError, TypeError) as e:
        print(f"❌ 数字解析失败: {e}")
        invited_num = accepted_num = completed_num = 0
    
    # 构建轮次统计
    current_revision = new_format_data.get("LatestRevisionNumber", 0)
    current_round = current_revision + 1
    
    group_stats = {
        current_revision: {
            "invited": invited_num,
            "accepted": accepted_num, 
            "completed": completed_num
        }
    }
    
    print(f"\n轮次统计数据:")
    print(f"  current_revision: {current_revision}")
    print(f"  current_round: {current_round}")
    print(f"  group_stats: {group_stats}")
    
    # 模拟模板渲染
    print(f"\n模板渲染效果:")
    for revision, stats in group_stats.items():
        round_num = revision + 1
        completion_rate = (stats['completed'] / stats['invited'] * 100) if stats['invited'] > 0 else 0
        print(f"  第 {round_num} 轮审稿:")
        print(f"    邀请: {stats['invited']} 人")
        print(f"    接受: {stats['accepted']} 人") 
        print(f"    完成: {stats['completed']} 人")
        print(f"    完成率: {completion_rate:.1f}%")

if __name__ == "__main__":
    test_data_processing()
