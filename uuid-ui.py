from flask import Flask, request, render_template_string, redirect, url_for
import requests
from datetime import datetime, timezone
import pytz

app = Flask(__name__)
CST = pytz.timezone('Asia/Shanghai')

def format_ts(ts):
    utc = datetime.fromtimestamp(ts, tz=timezone.utc)
    cst = utc.astimezone(CST)
    return cst.strftime('%Y-%m-%d %H:%M:%S')

def days_between(ts1, ts2=None):
    dt1 = datetime.fromtimestamp(ts1, tz=timezone.utc)
    dt2 = datetime.now(timezone.utc) if ts2 is None else datetime.fromtimestamp(ts2, tz=timezone.utc)
    delta = dt2 - dt1
    return delta.days, delta.seconds

def status_description(code, reviewer_map=None):
    mapping = {
        "1": "Submitted - 已提交",
        "2": "Editor Assigned - 编辑已分配",
        "3": "Under Review - 正在审稿中",
        "4": "Decision in Progress - 编辑评估中",
        "5": "Reviews Complete - 审稿已完成",
        "6": "Revision Required - 需要修订",
        "7": "Accepted - 已接受",
        "8": "Rejected - 已拒稿",
        "sub": "Submitted - 已提交",
        "ur": "Under Review - 正在审稿中",
        "rrc": "Reviews Received - 等待决定",
        "dip": "Decision in Progress - 编辑评估中",
        "rev": "Revision Required - 需要修订",
        "acc": "Accepted - 已接受",
        "rej": "Rejected - 已拒稿"
    }

    code_str = str(code).lower().strip()
    if code_str == "3" and reviewer_map:
        revisions = [rev for (_, rev) in reviewer_map]
        max_rev = max(revisions) if revisions else None
        if max_rev is not None:
            accepted = completed = 0
            for (rid, rev), events in reviewer_map.items():
                if rev != max_rev:
                    continue
                if any(ev["Event"] == "REVIEWER_ACCEPTED" for ev in events):
                    accepted += 1
                if any(ev["Event"] == "REVIEWER_COMPLETED" for ev in events):
                    completed += 1
            if accepted > 0 and accepted == completed:
                return mapping["rrc"]

    return mapping.get(code_str, f"未知状态（{code}）")

@app.route("/", methods=["GET", "POST"])
def index():
    if request.method == "POST":
        raw_input = request.form.get("uuid", "").strip()
        if "uuid=" in raw_input:
            import urllib.parse
            parsed = urllib.parse.urlparse(raw_input)
            params = urllib.parse.parse_qs(parsed.query)
            uuid = params.get("uuid", [""])[0]
        else:
            uuid = raw_input
        if uuid:
            return redirect(f"/result?uuid={uuid}")
    return render_template_string("""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>稿件状态查询</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background-color: #f8f9fa; }
            .form-box {
                max-width: 500px;
                margin: 100px auto;
                padding: 30px;
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
        </style>
        <script>
            window.onload = function() {
                const uuidInput = document.querySelector("input[name='uuid']");
                const lastUuid = localStorage.getItem("last_uuid");
                if (lastUuid && uuidInput) {
                    uuidInput.value = lastUuid;
                }
                document.querySelector("form").addEventListener("submit", function() {
                    localStorage.setItem("last_uuid", uuidInput.value);
                });
            }
        </script>
    </head>
    <body>
    <div class="container">
        <div class="form-box">
            <h4 class="mb-4 text-center text-primary">🔍 输入稿件UUID或Track链接查询状态</h4>
            <form method="POST">
                <div class="mb-3">
                    <input type="text" name="uuid" class="form-control form-control-lg" placeholder="请稿件UUID或Track链接" required>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">开始查询</button>
                </div>
            </form>
        </div>
    </div>
    </body>
    </html>
    """)

@app.route("/result")
def result():
    raw_uuid = request.args.get("uuid", "").strip()
    if "uuid=" in raw_uuid:
        import urllib.parse
        parsed = urllib.parse.urlparse(raw_uuid)
        params = urllib.parse.parse_qs(parsed.query)
        uuid = params.get("uuid", [""])[0]
    else:
        uuid = raw_uuid
    api_url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{uuid}"
    try:
        response = requests.get(api_url, timeout=10)
        if response.status_code != 200:
            raise ValueError("无效响应")

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("数据格式错误")

        manuscript_title = data.get("ManuscriptTitle")
        pubd_number = data.get("PubdNumber")
        journal_name = data.get("JournalName")
        first_author = data.get("FirstAuthor")
        corresponding_author = data.get("CorrespondingAuthor")
        last_updated = format_ts(data.get("LastUpdated"))
        submission_date = format_ts(data.get("SubmissionDate"))
        query_time = datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')

        reviewers = []
        reviewer_map = {}
        for ev in data.get("ReviewEvents", []):
            rid = ev["Id"]
            rev = ev["Revision"]
            reviewer_map.setdefault((rid, rev), []).append(ev)

        invited_count = len(set(k for k in reviewer_map))
        accepted_count = 0

        for (rid, rev), actions in reviewer_map.items():
            invite_time = None
            accept_time = None
            complete_time = None
            for a in actions:
                if a["Event"] == "REVIEWER_INVITED":
                    invite_time = a["Date"]
                elif a["Event"] == "REVIEWER_ACCEPTED":
                    accept_time = a["Date"]
                elif a["Event"] == "REVIEWER_COMPLETED":
                    complete_time = a["Date"]

            if not accept_time:
                continue
            accepted_count += 1
            dd, ss = days_between(invite_time, accept_time) if invite_time else (0, 0)
            ed, es = days_between(accept_time, complete_time) if complete_time else days_between(accept_time)
            review_time_str = f"{ed}天 {es//3600}小时 {es%3600//60}分 {es%60}秒" if complete_time else "尚未完成"

            reviewers.append({
                "id": rid,
                "revision": rev,
                "invite_time": format_ts(invite_time) if invite_time else "—",
                "accept_time": format_ts(accept_time),
                "complete_time": format_ts(complete_time) if complete_time else "未完成",
                "duration": f"{dd}天 {ss//3600}小时 {ss%3600//60}分 {ss%60}秒",
                "review_time": review_time_str,
                "review_elapsed": None if complete_time else f"{ed}天 {es//3600}小时 {es%3600//60}分 {es%60}秒"
            })

        group_stats = {}
        for (rid, rev), actions in reviewer_map.items():
            accepted = any(a["Event"] == "REVIEWER_ACCEPTED" for a in actions)
            completed = any(a["Event"] == "REVIEWER_COMPLETED" for a in actions)
            group_stats.setdefault(rev, {"accepted": 0, "completed": 0})
            if accepted:
                group_stats[rev]["accepted"] += 1
            if completed:
                group_stats[rev]["completed"] += 1

        status_str = status_description(data.get("Status"), reviewer_map)

        return render_template_string(TEMPLATE,
                                      title=manuscript_title,
                                      pubd_number=pubd_number,
                                      status=status_str,
                                      updated=last_updated,
                                      submitted=submission_date,
                                      invited_count=invited_count,
                                      accepted_count=accepted_count,
                                      reviewers=reviewers,
                                      query_time=query_time,
                                      journal=journal_name,
                                      first_author=first_author,
                                      corresponding_author=corresponding_author,
                                      group_stats=group_stats)
    except Exception:
        return render_template_string("""<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>查询失败</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body {
                    background-color: #f8f9fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .card {
                    max-width: 520px;
                    width: 100%;
                    padding: 2rem;
                    border-radius: 12px;
                    box-shadow: 0 0 12px rgba(0, 0, 0, 0.08);
                    background-color: white;
                }
                .highlight {
                    color: #d63384;
                    font-weight: bold;
                }
                code {
                    background-color: #f1f3f5;
                    padding: 0.2rem 0.4rem;
                    border-radius: 4px;
                    font-size: 0.95rem;
                }
            </style>
        </head>
        <body>
        <div class="card text-center">
                    <h4 class="mb-4 text-danger">❌ 查询失败</h4>
                    <p class="text-muted">
                        您输入的内容不是有效的 UUID 或 Track 链接。<br>
                        请确保输入的是 <strong>稿件状态链接</strong> 或其末尾的 <strong>UUID</strong>，而非稿件编号或投稿编号。<br>
                        正确格式示例：<br>
                        <code><span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code>
                        或<br>
                        <code>https://.../tracker?uuid=<span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code><br><br>
                        📬 提醒：该链接通常在稿件<strong>第一次送审后</strong>由系统发送至<strong>通讯作者邮箱</strong>，请注意查收相关邮件。
                    </p>
            <div class="d-grid gap-2 mt-4">
                <a href="/" class="btn btn-primary">🔙 返回重新输入</a>
            </div>
        </div>
        </body>
        </html>
        """)


TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>稿件状态监控</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body { background-color: #f5f7fa; }
        .badge-status { font-size: 1rem; }
        .badge-submitted { background-color: #0d6efd; }
        .badge-assigned { background-color: #6610f2; }
        .badge-review { background-color: #fd7e14; }
        .badge-rrc { background-color: #20c997; }
        .badge-dip { background-color: #ffc107; }
        .badge-rev { background-color: #6f42c1; }
        .badge-acc { background-color: #198754; }
        .badge-rej { background-color: #dc3545; }
        .review-card { border-left: 5px solid #0d6efd; }
        .section-title {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
<div class="container my-5">
    <h1 class="text-primary mb-4">📄 稿件状态监控系统</h1>

    <div class="card shadow-sm p-4 mb-4">
        <h4 class="section-title">📌 稿件基本信息</h4>
        <p><strong>标题：</strong>{{ title }}</p>
        <p><strong>编号：</strong>{{ pubd_number }}</p>
        <p><strong>期刊：</strong>{{ journal }}</p>
        <p><strong>第一作者：</strong>{{ first_author }}</p>
        <p><strong>通讯作者：</strong>{{ corresponding_author }}</p>
        <p><strong>当前状态：</strong>
            {% set badge_color = "badge-submitted" %}
            {% if "已提交" in status %} {% set badge_color = "badge-submitted" %}
            {% elif "已分配" in status %} {% set badge_color = "badge-assigned" %}
            {% elif "审稿中" in status %} {% set badge_color = "badge-review" %}
            {% elif "等待决定" in status %} {% set badge_color = "badge-rrc" %}
            {% elif "评估中" in status %} {% set badge_color = "badge-dip" %}
            {% elif "修订" in status %} {% set badge_color = "badge-rev" %}
            {% elif "已接受" in status %} {% set badge_color = "badge-acc" %}
            {% elif "已拒稿" in status %} {% set badge_color = "badge-rej" %}
            {% endif %}
            <span class="badge badge-status {{ badge_color }}">{{ status }}</span>
        </p>
        <p><strong>提交时间：</strong>{{ submitted }}</p>
        <p><strong>最近更新时间：</strong>{{ updated }}</p>
        <p><strong>已邀请审稿人：</strong>{{ invited_count }} 位</p>
        <p><strong>已接受邀请：</strong>{{ accepted_count }} 位</p>
    </div>

    <h4 class="section-title">👨‍⚖ 审稿人状态详情（可折叠查看每一轮）</h4>

    {% set grouped = {} %}
    {% for r in reviewers %}
        {% set _ = grouped.setdefault(r.revision, []).append(r) %}
    {% endfor %}

    <div class="accordion" id="reviewAccordion">
    {% for rev, group in grouped.items() %}
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ rev }}">
                <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ rev }}" aria-expanded="{% if loop.first %}true{% else %}false{% endif %}" aria-controls="collapse{{ rev }}">
                    第 {{ rev + 1 }} 轮审稿（接受 {{ group_stats[rev].accepted }} 人，完成 {{ group_stats[rev].completed }} 人）
                </button>
            </h2>
            <div id="collapse{{ rev }}" class="accordion-collapse collapse {% if loop.first %}show{% endif %}" aria-labelledby="heading{{ rev }}" data-bs-parent="#reviewAccordion">
                <div class="accordion-body">
                    <div class="row">
                    {% for r in group %}
                        <div class="col-md-6">
                            <div class="card shadow-sm mb-4 review-card">
                                <div class="card-body">
                                    <h5 class="card-title">审稿人 ID: {{ r.id }}</h5>
                                    <p><strong>📨 邀请时间：</strong> {{ r.invite_time }}</p>
                                    <p><strong>✅ 接受时间：</strong> {{ r.accept_time }}</p>
                                    <p><strong>📘 完成时间：</strong> {{ r.complete_time }}</p>
                                    <p><strong>⏱ 接受耗时：</strong> {{ r.duration }}</p>
                                    <p><strong>📗 审稿耗时：</strong> {{ r.review_time }}</p>
                                    {% if r.review_elapsed %}
                                        <p><strong>📙 已审稿时长：</strong> {{ r.review_elapsed }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}
    </div>

    <div class="text-center text-muted mt-4">
        页面数据为实时获取 | 所有时间均为北京时间<br>
        🕒 查询时间：{{ query_time }}
    </div>

    <div class="text-center mt-4">
        <a href="/" class="btn btn-outline-primary">🔄 返回继续查询</a>
    </div>
</div>
</body>
</html>
"""

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=2001)