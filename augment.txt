// ==UserScript==
// @name         AugmentCode自动注册
// @namespace    http://tampermonkey.net/
// @version      0.1.1
// @description  自动完成AugmentCode的注册流程
// <AUTHOR> name
// @match        https://*.augmentcode.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=augmentcode.com
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @connect      tempmail.plus
// ==/UserScript==

(function() {
    'use strict';

    // 主邮箱域名常量，用于生成标准格式的邮箱地址
    const EMAIL_DOMAIN = "@zerofive.icu";

    /**
     * 临时邮箱服务配置
     * 用于需要临时接收验证邮件的场景
     */
    const TEMP_MAIL_CONFIG = {
        username: "linwuzerofive",          // 临时邮箱用户名
        emailExtension: "@mailto.plus", // 临时邮箱扩展域名
        epin: ""             // 邮箱访问PIN码，用于登录临时邮箱
    };

    // 添加CSS动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        @keyframes slideInUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        #auto-register-log .log-entry {
            animation: slideInUp 0.3s ease-out;
        }

        #auto-register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #auto-register-btn:hover::before {
            left: 100%;
        }
    `;
    document.head.appendChild(style);




    // 用户名配置
    const USERNAME_CONFIG = {
        prefix: "linwu",           // 用户名前缀
        maxNumber: 9999,          // 最大编号
        syncUrl: "https://api.github.com/gists/", // GitHub Gist API用于同步
        gistId: "0b1bbed34f11ead359c4a0de10176f8e", // 需要替换为实际的Gist ID
        token: "****************************************" // 需要替换为实际的GitHub Token
    };


    // 颜色配置 - 更现代的配色方案
    const COLORS = {
        primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        primarySolid: '#667eea',
        secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        secondarySolid: '#f093fb',
        success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        successSolid: '#4facfe',
        danger: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        dangerSolid: '#fa709a',
        warning: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        warningSolid: '#fcb69f',
        info: '#6c7b7f',
        light: '#f8f9fa',
        dark: '#2d3748',
        background: 'linear-gradient(135deg, rgba(45, 55, 72, 0.98) 0%, rgba(68, 90, 120, 0.95) 100%)',
        cardBg: 'rgba(255, 255, 255, 0.08)',
        border: 'rgba(255, 255, 255, 0.12)',
        text: '#e2e8f0',
        textMuted: '#a0aec0'
    };

    // 日志UI配置 - 增强的配置选项
    const LOG_UI_CONFIG = {
        position: {
            bottom: 40,
            left: 20
        },
        dimensions: {
            width: 380,
            maxHeight: 500
        },
        animation: {
            duration: '0.4s',
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }
    };

    // 创建日志UI - 现代化设计风格
    function createLogUI() {
        const logContainer = document.createElement('div');
        logContainer.id = "auto-register-log";
        logContainer.style.cssText = `
            position: fixed;
            bottom: ${LOG_UI_CONFIG.position.bottom}px;
            left: ${LOG_UI_CONFIG.position.left}px;
            width: ${LOG_UI_CONFIG.dimensions.width}px;
            max-height: ${LOG_UI_CONFIG.dimensions.maxHeight}px;
            background: ${COLORS.background};
            border-radius: 16px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 8px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid ${COLORS.border};
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: translateY(0);
            transition: all ${LOG_UI_CONFIG.animation.duration} ${LOG_UI_CONFIG.animation.easing};
        `;

        logContainer.innerHTML = `
            <div style="
                padding: 18px 20px;
                background: ${COLORS.primary};
                color: white;
                font-weight: 700;
                font-size: 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: none;
                position: relative;
                overflow: hidden;
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="
                        display: inline-block;
                        width: 8px;
                        height: 8px;
                        background: #00ff88;
                        border-radius: 50%;
                        box-shadow: 0 0 10px #00ff88;
                        animation: pulse 2s infinite;
                    "></span>
                    <span>🚀 自动注册助手</span>
                </div>
                <div style="display: flex; gap: 8px; align-items: center;">
                    <button id="auto-register-btn" style="
                        background: ${COLORS.secondary};
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 600;
                        padding: 8px 16px;
                        border-radius: 20px;
                        display: none;
                        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
                        position: relative;
                        overflow: hidden;
                    ">
                        <span style="position: relative; z-index: 1;">开始注册</span>
                    </button>
                    <button id="config-btn" style="
                        background: ${COLORS.cardBg};
                        border: 1px solid ${COLORS.border};
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 8px 12px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    ">⚙️</button>
                    <button id="clear-log" style="
                        background: ${COLORS.cardBg};
                        border: 1px solid ${COLORS.border};
                        color: white;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 8px 12px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    ">🗑️</button>
                    <button id="minimize-log" style="
                        background: ${COLORS.cardBg};
                        border: 1px solid ${COLORS.border};
                        color: white;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 600;
                        padding: 8px 12px;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    ">−</button>
                </div>
            </div>
            <div style="
                padding: 12px 20px;
                background: ${COLORS.cardBg};
                border-bottom: 1px solid ${COLORS.border};
                font-size: 13px;
                color: ${COLORS.textMuted};
                display: flex;
                align-items: center;
                justify-content: space-between;
                backdrop-filter: blur(10px);
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="
                        font-size: 16px;
                        filter: drop-shadow(0 0 4px currentColor);
                    ">💻</span>
                    <span style="font-weight: 500;">操作控制台</span>
                </div>
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 11px;
                    color: ${COLORS.textMuted};
                ">
                    <span style="
                        width: 6px;
                        height: 6px;
                        background: ${COLORS.successSolid};
                        border-radius: 50%;
                        display: inline-block;
                        animation: pulse 2s infinite;
                    "></span>
                    <span>在线</span>
                </div>
            </div>
            <div id="log-content" style="
                padding: 20px;
                overflow-y: auto;
                max-height: calc(${LOG_UI_CONFIG.dimensions.maxHeight}px - 160px);
                font-size: 13px;
                color: ${COLORS.text};
                line-height: 1.6;
                background: rgba(0, 0, 0, 0.1);
                scrollbar-width: thin;
                scrollbar-color: ${COLORS.border} transparent;
            "></div>
        `;

        document.body.appendChild(logContainer);

        // 最小化功能
        let isMinimized = false;
        const logContent = document.getElementById('log-content');
        const minimizeBtn = document.getElementById('minimize-log');

        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            logContent.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.textContent = isMinimized ? '□' : '−';
            logContainer.style.transform = isMinimized ? 'scale(0.95)' : 'scale(1)';
        });

        // 清除日志功能
        const clearBtn = document.getElementById('clear-log');
        clearBtn.addEventListener('click', () => {
            logContent.innerHTML = '';
            log('日志已清除', 'info');
        });

        // 配置功能
        const configBtn = document.getElementById('config-btn');
        configBtn.addEventListener('click', () => {
            showConfigDialog();
        });

        // 增强的按钮交互效果
        const registerBtn = document.getElementById('auto-register-btn');

        if (registerBtn) {
            registerBtn.addEventListener('mouseenter', () => {
                registerBtn.style.transform = 'scale(1.05) translateY(-1px)';
                registerBtn.style.boxShadow = '0 6px 20px rgba(240, 147, 251, 0.4)';
            });
            registerBtn.addEventListener('mouseleave', () => {
                registerBtn.style.transform = 'scale(1) translateY(0)';
                registerBtn.style.boxShadow = '0 4px 12px rgba(240, 147, 251, 0.3)';
            });
        }

        [clearBtn, minimizeBtn, configBtn].forEach(btn => {
            if (btn) {
                btn.addEventListener('mouseenter', () => {
                    btn.style.transform = 'scale(1.1)';
                    btn.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
                });
                btn.addEventListener('mouseleave', () => {
                    btn.style.transform = 'scale(1)';
                    btn.style.backgroundColor = COLORS.cardBg;
                });
            }
        });

        return {
            log: function(message, type = 'info') {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.style.marginBottom = '12px';
                logEntry.style.padding = '14px 16px';
                logEntry.style.borderRadius = '10px';
                logEntry.style.wordBreak = 'break-word';
                logEntry.style.transition = 'all 0.3s ease';
                logEntry.style.border = '1px solid transparent';
                logEntry.style.position = 'relative';
                logEntry.style.overflow = 'hidden';

                let bgColor, textColor, borderColor, icon;

                switch(type) {
                    case 'success':
                        bgColor = 'rgba(79, 172, 254, 0.15)';
                        textColor = COLORS.successSolid;
                        borderColor = 'rgba(79, 172, 254, 0.3)';
                        icon = '✅';
                        break;
                    case 'error':
                        bgColor = 'rgba(250, 112, 154, 0.15)';
                        textColor = COLORS.dangerSolid;
                        borderColor = 'rgba(250, 112, 154, 0.3)';
                        icon = '❌';
                        break;
                    case 'warning':
                        bgColor = 'rgba(252, 182, 159, 0.15)';
                        textColor = COLORS.warningSolid;
                        borderColor = 'rgba(252, 182, 159, 0.3)';
                        icon = '⚠️';
                        break;
                    default:
                        bgColor = COLORS.cardBg;
                        textColor = COLORS.text;
                        borderColor = COLORS.border;
                        icon = 'ℹ️';
                }

                logEntry.style.backgroundColor = bgColor;
                logEntry.style.color = textColor;
                logEntry.style.borderColor = borderColor;

                const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute:'2-digit', second:'2-digit' });
                logEntry.innerHTML = `
                    <div style="display: flex; align-items: flex-start; gap: 10px;">
                        <span style="font-size: 14px; margin-top: 1px;">${icon}</span>
                        <div style="flex: 1;">
                            <div style="font-size: 11px; color: ${COLORS.textMuted}; margin-bottom: 4px;">${time}</div>
                            <div style="font-weight: 500; line-height: 1.4;">${message}</div>
                        </div>
                    </div>
                `;

                logContent.appendChild(logEntry);
                logContent.scrollTop = logContent.scrollHeight;

                // 添加悬停效果
                logEntry.addEventListener('mouseenter', () => {
                    logEntry.style.transform = 'translateX(4px)';
                    logEntry.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                });
                logEntry.addEventListener('mouseleave', () => {
                    logEntry.style.transform = 'translateX(0)';
                    logEntry.style.boxShadow = 'none';
                });
            },
            showRegisterButton: function() {
                const registerBtn = document.getElementById('auto-register-btn');
                if (registerBtn) {
                    this.log('找到注册按钮，正在显示...');
                    registerBtn.style.display = 'inline-block';
                    return registerBtn;
                } else {
                    this.log('未找到注册按钮元素', 'error');
                    return null;
                }
            },
            showUsedNumbers: async function() {
                try {
                    const usedNumbers = await getUsedNumbers();
                    const nextNumber = usedNumbers.length > 0 ? Math.max(...usedNumbers) + 1 : 1;
                    this.log(`已使用编号: ${usedNumbers.length} 个，下一个编号: linwu${nextNumber}`, 'info');
                } catch (error) {
                    this.log('获取编号状态失败: ' + error.message, 'error');
                }
            },

            // 显示状态指示器
            showStatus: function(message, type = 'info') {
                // 移除旧的状态指示器
                const oldIndicator = document.getElementById('status-indicator');
                if (oldIndicator) {
                    oldIndicator.remove();
                }

                const indicator = document.createElement('div');
                indicator.id = 'status-indicator';
                indicator.textContent = message;

                const colors = {
                    info: COLORS.primary,
                    success: COLORS.success,
                    warning: COLORS.warning,
                    error: COLORS.error
                };

                indicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    padding: 12px 20px;
                    background: ${colors[type] || COLORS.primary};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    max-width: 300px;
                    word-wrap: break-word;
                    animation: slideIn 0.3s ease;
                `;

                // 添加动画样式
                if (!document.getElementById('status-animation-style')) {
                    const style = document.createElement('style');
                    style.id = 'status-animation-style';
                    style.textContent = `
                        @keyframes slideIn {
                            from { transform: translateX(100%); opacity: 0; }
                            to { transform: translateX(0); opacity: 1; }
                        }
                    `;
                    document.head.appendChild(style);
                }

                document.body.appendChild(indicator);

                // 5秒后自动移除（除非是错误信息）
                if (type !== 'error') {
                    setTimeout(() => {
                        if (indicator && indicator.parentNode) {
                            indicator.remove();
                        }
                    }, 5000);
                }

                return indicator;
            },


        };
    }

    // 显示配置对话框
    function showConfigDialog() {
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            z-index: 20000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // 创建配置对话框
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: ${COLORS.background};
            border-radius: 16px;
            padding: 30px;
            width: 500px;
            max-width: 90vw;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid ${COLORS.border};
            color: ${COLORS.text};
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        const currentGistId = GM_getValue('gistId', '');
        const currentToken = GM_getValue('githubToken', '');

        dialog.innerHTML = `
            <h3 style="margin: 0 0 20px 0; color: ${COLORS.text}; font-size: 18px;">⚙️ 同步配置</h3>
            <div style="margin-bottom: 20px;">
                <p style="margin: 0 0 15px 0; color: ${COLORS.textMuted}; font-size: 14px; line-height: 1.5;">
                    为了在不同设备间同步已使用的编号，需要配置GitHub Gist。<br>
                    <a href="https://gist.github.com/" target="_blank" style="color: ${COLORS.primarySolid};">创建新的Gist</a>
                    并获取 <a href="https://github.com/settings/tokens" target="_blank" style="color: ${COLORS.primarySolid};">GitHub Token</a>
                </p>

                <label style="display: block; margin-bottom: 8px; font-weight: 500;">Gist ID:</label>
                <input type="text" id="gist-id" value="${currentGistId}" placeholder="例如: abc123def456..." style="
                    width: 100%;
                    padding: 12px;
                    border: 1px solid ${COLORS.border};
                    border-radius: 8px;
                    background: ${COLORS.cardBg};
                    color: ${COLORS.text};
                    font-size: 14px;
                    margin-bottom: 15px;
                    box-sizing: border-box;
                ">

                <label style="display: block; margin-bottom: 8px; font-weight: 500;">GitHub Token:</label>
                <input type="password" id="github-token" value="${currentToken}" placeholder="ghp_xxxxxxxxxxxx..." style="
                    width: 100%;
                    padding: 12px;
                    border: 1px solid ${COLORS.border};
                    border-radius: 8px;
                    background: ${COLORS.cardBg};
                    color: ${COLORS.text};
                    font-size: 14px;
                    margin-bottom: 20px;
                    box-sizing: border-box;
                ">
            </div>

            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button id="cancel-config" style="
                    background: ${COLORS.cardBg};
                    border: 1px solid ${COLORS.border};
                    color: ${COLORS.text};
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                ">取消</button>
                <button id="save-config" style="
                    background: ${COLORS.primary};
                    border: none;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                ">保存</button>
            </div>
        `;

        overlay.appendChild(dialog);
        document.body.appendChild(overlay);

        // 事件处理
        document.getElementById('cancel-config').addEventListener('click', () => {
            document.body.removeChild(overlay);
        });

        document.getElementById('save-config').addEventListener('click', () => {
            const gistId = document.getElementById('gist-id').value.trim();
            const token = document.getElementById('github-token').value.trim();

            if (gistId && token) {
                GM_setValue('gistId', gistId);
                GM_setValue('githubToken', token);

                // 更新配置
                USERNAME_CONFIG.gistId = gistId;
                USERNAME_CONFIG.token = token;

                logger.log('配置保存成功！', 'success');
                document.body.removeChild(overlay);
            } else {
                alert('请填写完整的配置信息');
            }
        });

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });
    }

    // 初始化配置
    function initConfig() {
        const savedGistId = GM_getValue('gistId', '');
        const savedToken = GM_getValue('githubToken', '');

        if (savedGistId && savedToken) {
            USERNAME_CONFIG.gistId = savedGistId;
            USERNAME_CONFIG.token = savedToken;
        }
    }

    // 创建全局日志对象
    const logger = createLogUI();

    // 初始化配置
    initConfig();

    // 从云端获取已使用的编号列表
    async function getUsedNumbers() {
        try {
            const response = await fetch(`${USERNAME_CONFIG.syncUrl}${USERNAME_CONFIG.gistId}`, {
                headers: {
                    'Authorization': `token ${USERNAME_CONFIG.token}`,
                    'Accept': 'application/vnd.github.v3+json'
                }
            });

            if (response.ok) {
                const gist = await response.json();
                const content = gist.files['used_numbers.json']?.content;
                if (content) {
                    const data = JSON.parse(content);
                    logger.log(`从云端获取到 ${data.usedNumbers.length} 个已使用编号`);
                    return data.usedNumbers || [];
                }
            }
        } catch (error) {
            logger.log(`获取云端数据失败: ${error.message}`, 'warning');
        }

        // 如果云端获取失败，尝试从本地存储获取
        const localData = GM_getValue('usedNumbers', '[]');
        const usedNumbers = JSON.parse(localData);
        logger.log(`从本地获取到 ${usedNumbers.length} 个已使用编号`);
        return usedNumbers;
    }

    // 更新云端已使用编号列表
    async function updateUsedNumbers(usedNumbers) {
        try {
            const data = {
                usedNumbers: usedNumbers,
                lastUpdated: new Date().toISOString()
            };

            const response = await fetch(`${USERNAME_CONFIG.syncUrl}${USERNAME_CONFIG.gistId}`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `token ${USERNAME_CONFIG.token}`,
                    'Accept': 'application/vnd.github.v3+json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    files: {
                        'used_numbers.json': {
                            content: JSON.stringify(data, null, 2)
                        }
                    }
                })
            });

            if (response.ok) {
                logger.log('云端数据更新成功', 'success');
                return true;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            logger.log(`更新云端数据失败: ${error.message}`, 'warning');
            return false;
        }
    }

    // 生成顺序邮箱
    async function generateEmail() {
        try {
            // 获取已使用的编号
            const usedNumbers = await getUsedNumbers();

            // 找到下一个可用编号
            let nextNumber = 1;
            while (usedNumbers.includes(nextNumber) && nextNumber <= USERNAME_CONFIG.maxNumber) {
                nextNumber++;
            }

            if (nextNumber > USERNAME_CONFIG.maxNumber) {
                throw new Error(`已达到最大编号限制 (${USERNAME_CONFIG.maxNumber})`);
            }

            const username = `${USERNAME_CONFIG.prefix}${nextNumber}`;
            const email = `${username}${EMAIL_DOMAIN}`;

            // 更新已使用编号列表
            usedNumbers.push(nextNumber);
            usedNumbers.sort((a, b) => a - b); // 排序

            // 保存到本地
            GM_setValue('usedNumbers', JSON.stringify(usedNumbers));

            // 尝试同步到云端
            await updateUsedNumbers(usedNumbers);

            logger.log(`生成邮箱: ${email} (编号: ${nextNumber})`, 'success');
            return email;

        } catch (error) {
            logger.log(`生成邮箱失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return null;
    }

    // 查找包含特定文本的元素
    function findElementByText(text, tagNames = ['button', 'a', 'span', 'div']) {
        for (const tagName of tagNames) {
            const elements = Array.from(document.querySelectorAll(tagName));
            const found = elements.find(el =>
                el.textContent && el.textContent.toLowerCase().includes(text.toLowerCase())
            );
            if (found) return found;
        }
        return null;
    }

    // 从邮件文本中提取验证码
    function extractVerificationCode(mailText) {
        const codeMatch = mailText.match(/(?<![a-zA-Z@.])\b\d{6}\b/);
        return codeMatch ? codeMatch[0] : null;
    }

    // 删除邮件
    async function deleteEmail(firstId) {
        return new Promise((resolve, reject) => {
            const deleteUrl = 'https://tempmail.plus/api/mails/';
            const maxRetries = 5;
            let retryCount = 0;

            function tryDelete() {
                GM_xmlhttpRequest({
                    method: "DELETE",
                    url: deleteUrl,
                    data: `email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&first_id=${firstId}&epin=${TEMP_MAIL_CONFIG.epin}`,
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText).result;
                            if (result === true) {
                                logger.log("邮件删除成功", 'success');
                                resolve(true);
                                return;
                            }
                        } catch (error) {
                            logger.log("解析删除响应失败: " + error, 'warning');
                        }

                        // 如果还有重试次数，继续尝试
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件失败，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败，已达到最大重试次数", 'error');
                            resolve(false);
                        }
                    },
                    onerror: function(error) {
                        if (retryCount < maxRetries - 1) {
                            retryCount++;
                            logger.log(`删除邮件出错，正在重试 (${retryCount}/${maxRetries})...`, 'warning');
                            setTimeout(tryDelete, 500);
                        } else {
                            logger.log("删除邮件失败: " + error, 'error');
                            resolve(false);
                        }
                    }
                });
            }

            tryDelete();
        });
    }

    // 获取最新邮件中的验证码
    async function getLatestMailCode() {
        return new Promise((resolve, reject) => {
            const mailListUrl = `https://tempmail.plus/api/mails?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&limit=20&epin=${TEMP_MAIL_CONFIG.epin}`;

            GM_xmlhttpRequest({
                method: "GET",
                url: mailListUrl,
                onload: async function(mailListResponse) {
                    try {
                        const mailListData = JSON.parse(mailListResponse.responseText);
                        if (!mailListData.result || !mailListData.first_id) {
                            resolve(null);
                            return;
                        }

                        const firstId = mailListData.first_id;
                        const mailDetailUrl = `https://tempmail.plus/api/mails/${firstId}?email=${TEMP_MAIL_CONFIG.username}${TEMP_MAIL_CONFIG.emailExtension}&epin=${TEMP_MAIL_CONFIG.epin}`;

                        GM_xmlhttpRequest({
                            method: "GET",
                            url: mailDetailUrl,
                            onload: async function(mailDetailResponse) {
                                try {
                                    const mailDetailData = JSON.parse(mailDetailResponse.responseText);
                                    if (!mailDetailData.result) {
                                        resolve(null);
                                        return;
                                    }

                                    const mailText = mailDetailData.text || "";
                                    const mailSubject = mailDetailData.subject || "";
                                    logger.log("找到邮件主题: " + mailSubject);

                                    const code = extractVerificationCode(mailText);

                                    // 获取到验证码后，尝试删除邮件
                                    if (code) {
                                        await deleteEmail(firstId);
                                    }

                                    resolve(code);
                                } catch (error) {
                                    logger.log("解析邮件详情失败: " + error, 'error');
                                    resolve(null);
                                }
                            },
                            onerror: function(error) {
                                logger.log("获取邮件详情失败: " + error, 'error');
                                resolve(null);
                            }
                        });
                    } catch (error) {
                        logger.log("解析邮件列表失败: " + error, 'error');
                        resolve(null);
                    }
                },
                onerror: function(error) {
                    logger.log("获取邮件列表失败: " + error, 'error');
                    resolve(null);
                }
            });
        });
    }

    // 获取验证码（带重试机制）
    async function getVerificationCode(maxRetries = 6, retryInterval = 5000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            logger.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);

            try {
                const code = await getLatestMailCode();
                if (code) {
                    logger.log("成功获取验证码: " + code, 'success');
                    return code;
                }

                if (attempt < maxRetries - 1) {
                    logger.log(`未获取到验证码，${retryInterval/1000}秒后重试...`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                logger.log("获取验证码出错: " + error, 'error');
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }

        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码。`);
    }

    // 自动填写邮箱并提交
    async function fillEmail() {
        const email = await generateEmail();
        logger.log('使用邮箱: ' + email);

        const emailInput = await waitForElement('input[name="username"]');
        if (!emailInput) {
            logger.log('未找到邮箱输入框', 'error');
            return false;
        }

        logger.log('找到邮箱输入框，开始填写');

        // 填写邮箱
        emailInput.value = email;
        emailInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 填写验证码
    async function fillVerificationCode() {
        const code = await getVerificationCode();
        if (!code) {
            logger.log('未能获取验证码', 'error');
            return false;
        }

        const codeInput = await waitForElement('input[name="code"]');
        if (!codeInput) {
            logger.log('未找到验证码输入框', 'error');
            return false;
        }

        // 填写验证码
        codeInput.value = code;
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));

        // 点击继续按钮
        const continueBtn = await waitForElement('button[type="submit"]');
        if (!continueBtn) {
            logger.log('未找到继续按钮', 'error');
            return false;
        }

        continueBtn.click();
        return true;
    }

    // 同意服务条款并完成注册
    async function completeRegistration() {
        const checkbox = await waitForElement('input[type="checkbox"]');
        if (checkbox) {
            if (!checkbox.checked) {
                checkbox.click();
                logger.log('已自动勾选服务条款同意框', 'success');
            }
        } else {
            logger.log('未找到服务条款复选框', 'warning');
        }

        const signupBtn = findElementByText('Sign up', ['button']) ||
                         await waitForElement('button[type="submit"]');
        if (!signupBtn) {
            logger.log('未找到注册按钮', 'error');
            return false;
        }

        signupBtn.click();
        logger.log('点击注册按钮', 'success');
        return true;
    }






    // 主函数
    async function main() {
        // 只在注册相关页面运行
        if (!window.location.href.includes('login.augmentcode.com') && !window.location.href.includes('auth.augmentcode.com')) {
            logger.log('当前页面不是注册页面，脚本不执行', 'info');
            return;
        }

        logger.log('===== 开始自动注册流程 =====', 'info');

        // 显示当前编号状态
        await logger.showUsedNumbers();

        // 检查当前页面状态
        const emailInput = document.querySelector('input[name="username"]');
        const codeInput = document.querySelector('input[name="code"]');
        const termsCheckbox = document.querySelector('#terms-of-service-checkbox');

        if (emailInput) {
            logger.log('检测到邮箱输入页面');
            // 显示注册按钮
            const registerButton = logger.showRegisterButton();
            if (registerButton) {
                registerButton.addEventListener('click', async () => {
                    try {
                        registerButton.disabled = true;
                        registerButton.textContent = '处理中...';
                        registerButton.style.background = COLORS.warning;

                        if (await fillEmail()) {
                            logger.log('邮箱填写完成，请等待页面跳转到验证码输入...', 'success');
                        }
                    } catch (error) {
                        logger.log('填写邮箱过程出错: ' + error, 'error');
                    } finally {
                        registerButton.disabled = false;
                        registerButton.textContent = '开始注册';
                        registerButton.style.background = COLORS.secondary;
                    }
                });
            }
        } else if (codeInput) {
            logger.log('检测到验证码输入页面，自动执行验证码填写...');
            try {
                if (await fillVerificationCode()) {
                    logger.log('验证码填写完成，正在完成注册...', 'success');
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    if (await completeRegistration()) {
                        logger.log('===== 注册流程完成！ =====', 'success');
                    }
                }
            } catch (error) {
                logger.log('填写验证码过程出错: ' + error, 'error');
            }
        } else if (termsCheckbox) {
            logger.log('检测到服务条款页面，自动勾选同意框...');
            try {
                if (!termsCheckbox.checked) {
                    termsCheckbox.click();
                    logger.log('已自动勾选服务条款同意框', 'success');
                }

                // 查找并点击注册按钮
                const signupBtn = await waitForElement('button[type="button"]');
                if (signupBtn) {
                    signupBtn.click();
                    logger.log('点击注册按钮完成', 'success');
                }
            } catch (error) {
                logger.log('勾选服务条款过程出错: ' + error, 'error');
            }
        } else {
            logger.log('无法识别当前页面状态', 'warning');
        }
    }

    // 启动脚本
    main().catch(error => logger.log('脚本执行出错: ' + error, 'error'));
})();
