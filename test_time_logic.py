#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的时间计算逻辑
"""

import time
from datetime import datetime, timezone, timedelta

def display_time(timestamp):
    """转换时间戳为本地时间"""
    if not timestamp:
        return datetime.now()
    
    # 处理时间戳
    if isinstance(timestamp, str):
        timestamp = float(timestamp)
    
    # 转换为UTC时间，然后转为本地时间
    utc_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    local_time = utc_time.astimezone()
    return local_time

def calculate_time_stats(start_time, end_time=None):
    """计算时间统计信息"""
    if not start_time:
        return {"days": 0, "hours": 0, "minutes": 0, "total_seconds": 0, "formatted": "0天0小时"}
    
    start_dt = display_time(start_time)
    end_dt = display_time(end_time) if end_time else datetime.now().astimezone()
    
    delta = end_dt - start_dt
    total_seconds = int(delta.total_seconds())
    
    if total_seconds < 0:
        total_seconds = 0
    
    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    
    formatted = f"{days}天{hours}小时"
    
    return {
        "days": days,
        "hours": hours,
        "minutes": (total_seconds % 3600) // 60,
        "total_seconds": total_seconds,
        "formatted": formatted
    }

def test_time_logic():
    """测试时间逻辑"""
    print("=== 测试时间计算逻辑 ===")
    
    # 模拟数据
    submission_date = 1750627742  # 2025-07-23
    last_updated_ongoing = int(time.time())  # 现在
    last_updated_final = 1754744192  # 2025-08-09 (假设的最终决定时间)
    
    print(f"投稿时间: {display_time(submission_date)}")
    print(f"当前时间: {display_time(last_updated_ongoing)}")
    print(f"最终决定时间: {display_time(last_updated_final)}")
    
    # 测试不同状态
    test_cases = [
        {"status": 3, "name": "正在审稿中", "last_updated": last_updated_ongoing},
        {"status": 9, "name": "已接受", "last_updated": last_updated_final},
        {"status": 39, "name": "已拒稿", "last_updated": last_updated_final},
        {"status": 5, "name": "编辑评估中", "last_updated": last_updated_ongoing},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['name']} (状态码: {case['status']}) ---")
        
        current_status = case['status']
        last_updated = case['last_updated']
        
        # 判断是否为终态（已接受或已拒稿）
        is_final_status = str(current_status) in ["9", "39"]  # 9=已接受, 39=已拒稿
        
        if is_final_status:
            # 终态：计算流程天数（从提交到最终决定）
            submission_stats = calculate_time_stats(submission_date, last_updated)
            submission_stats['label'] = '流程天数'
            submission_stats['description'] = '从投稿到最终决定的总时间'
            print(f"✅ 终态 - 使用流程天数")
        else:
            # 进行中：计算投稿天数（从提交到现在）
            submission_stats = calculate_time_stats(submission_date)
            submission_stats['label'] = '投稿天数'
            submission_stats['description'] = '从投稿到现在的时间'
            print(f"⏳ 进行中 - 使用投稿天数")
        
        print(f"标签: {submission_stats['label']}")
        print(f"描述: {submission_stats['description']}")
        print(f"天数: {submission_stats['days']} 天")
        print(f"格式化: {submission_stats['formatted']}")

if __name__ == "__main__":
    test_time_logic()
