# 合并后的完整 Flask 应用（统一运行在 5001 端口）

from flask import Flask, render_template_string, request, redirect, url_for, session, flash
from flask_apscheduler import APScheduler
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Email, NumberRange
import requests
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timezone
import pytz
import json
import hashlib
import os
from datetime import datetime, timezone, timedelta
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
scheduler = APScheduler()
scheduler.init_app(app)
scheduler.start()

# ============ 共用配置和工具函数 ============
CST = timezone(timedelta(hours=8))  # 明确设定北京时间
monitor_tasks = {}
MAX_TASKS = 10
MAX_LOG_LINES = 50
ADMIN_USERNAME = "3313637051"
ADMIN_PASSWORD = "aidi+6898"

status_mapping = {
    "1": "Submitted - 已提交",
    "2": "Editor Assigned - 编辑已分配",
    "3": "Under Review - 正在审稿中",
    "4": "Reviews Completed (RRC) - 审稿已完成",
    "5": "Decision in Progress - 编辑评估中",
    "6": "Decision in Process - 决定中",
    "7": "Revision Required - 需要修订",  # ✅ 正确
    "8": "With Editor - 编辑评估中",
    "9": "Accepted - 已接受",  # ✅ 正确
    "39": "Rejected - 已拒稿",  # ✅ 正确
    "29": "Decision in Progress - 编辑评估中",
    "sub": "Submitted - 已提交",
    "ur": "Under Review - 正在审稿中",
    "rrc": "Reviews Received - 等待决定",
    "dip": "Decision in Progress - 编辑评估中",
    "rev": "Revision Required - 需要修订",
    "acc": "Accepted - 已接受",
    "rej": "Rejected - 已拒稿"
}





def display_time(ts):
    return datetime.fromtimestamp(ts, tz=timezone.utc).astimezone(CST) + timedelta(hours=5)


def days_between(ts1, ts2=None):
    dt1 = datetime.fromtimestamp(ts1, tz=timezone.utc)
    dt2 = datetime.now(timezone.utc) if ts2 is None else datetime.fromtimestamp(ts2, tz=timezone.utc)
    delta = dt2 - dt1
    return delta.days, delta.seconds


def status_description(code, reviewer_map=None):
    mapping = {
        "1": "Submitted - 已提交",
        "2": "Editor Assigned - 编辑已分配",
        "3": "Under Review - 正在审稿中",
        "4": "Reviews Completed (RRC) - 审稿已完成",
        "5": "Decision in Progress - 编辑评估中",
        "6": "Decision in Process - 决定中",
        "7": "Revision Required - 需要修订",  # ✅ 正确
        "8": "With Editor - 编辑评估中",
        "9": "Accepted - 已接受",  # ✅ 正确
        "39": "Rejected - 已拒稿",  # ✅ 正确
        "29": "Decision in Progress - 编辑评估中",
        "sub": "Submitted - 已提交",
        "ur": "Under Review - 正在审稿中",
        "rrc": "Reviews Received - 等待决定",
        "dip": "Decision in Progress - 编辑评估中",
        "rev": "Revision Required - 需要修订",
        "acc": "Accepted - 已接受",
        "rej": "Rejected - 已拒稿"
    }

    code_str = str(code).lower().strip()
    if code_str == "3" and reviewer_map:
        revisions = [rev for (_, rev) in reviewer_map]
        max_rev = max(revisions) if revisions else None
        if max_rev is not None:
            accepted = completed = 0
            for (rid, rev), events in reviewer_map.items():
                if rev != max_rev:
                    continue
                if any(ev["Event"] == "REVIEWER_ACCEPTED" for ev in events):
                    accepted += 1
                if any(ev["Event"] == "REVIEWER_COMPLETED" for ev in events):
                    completed += 1
            if accepted > 0 and accepted == completed:
                return mapping["rrc"]

    return mapping.get(code_str, f"未知状态（{code}）")


def status_text(code):
    return status_mapping.get(str(code).lower(), f"未知状态（{code}）")


def generate_hash(obj_list):
    filtered = [
        {"Id": ev["Id"], "Revision": ev["Revision"], "Event": ev["Event"], "Date": ev["Date"]}
        for ev in obj_list
    ]
    return hashlib.md5(json.dumps(filtered, sort_keys=True).encode()).hexdigest()


def write_log(recipient_email, uuid, message, threshold=None):
    global MAX_LOG_LINES
    threshold = threshold if threshold is not None else MAX_LOG_LINES
    masked = recipient_email[:3] + '***' + recipient_email[-3:]
    filename = f"{masked}_{uuid}_log.txt"
    now = datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')
    new_entry = f"[{now}] ({masked}) {message}\n"

    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        else:
            lines = []

        lines.append(new_entry)

        # 判断是否为关键日志
        def is_important(entry):
            return any(key in entry for key in ['📢 通知发送', '📧 邮件已发送'])

        # 分离重要和非重要日志
        important_logs = [line for line in lines if is_important(line)]
        other_logs = [line for line in lines if not is_important(line)]

        # 限制非重要日志保留条数
        if len(other_logs) > threshold:
            other_logs = other_logs[-threshold:]

        # 合并保存
        new_lines = other_logs + important_logs
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)

    except Exception as e:
        print(f"日志写入错误: {e}")


def send_email(subject, body, sender_email, password, recipient_email, uuid=None):
    msg = MIMEText(body.replace('\n', '<br>'), 'html')
    msg['Subject'] = subject
    msg['From'] = sender_email
    msg['To'] = recipient_email
    try:
        if not sender_email.endswith('@qq.com'):
            raise ValueError("仅支持 QQ 邮箱作为发件人")
        server = smtplib.SMTP('smtp.qq.com', 587)
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, [recipient_email], msg.as_string())
        server.quit()
        if uuid:
            write_log(recipient_email, uuid, f"📧 邮件已发送: {subject}", threshold=MAX_LOG_LINES)
        return True
    except Exception as e:
        if uuid:
            write_log(recipient_email, uuid, f"❌ 邮件发送失败: {e}", threshold=MAX_LOG_LINES)
        return False


# 接下来继续补全完整的监控任务功能和 UUID 查询功能（合并）
# （前略）前面部分略去，这里继续追加 Flask 表单、任务检查、监控视图、UUID 查询视图等功能

# ============ Flask 表单定义 ============
class MonitorForm(FlaskForm):
    sender_email = StringField('发件人邮箱(仅支持QQ邮箱)', validators=[DataRequired(), Email()])
    password = PasswordField('邮箱授权码', validators=[DataRequired()])
    recipient_email = StringField('收件人邮箱(支持所有邮箱)', validators=[DataRequired(), Email()])
    uuid = StringField('稿件UUID或Track链接', validators=[DataRequired()])
    interval = IntegerField('检查间隔（分钟）', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('开始监控')


class LoginForm(FlaskForm):
    username = StringField('管理员账号', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])
    submit = SubmitField('登录')


class ConfigForm(FlaskForm):
    log_lines = IntegerField('每个日志最多保留条数', validators=[DataRequired(), NumberRange(min=10, max=1000)])
    max_tasks = IntegerField('最大监控任务数', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('更新设置')


# ============ 检查任务函数 ============
def check_status(task_id):
    task = monitor_tasks.get(task_id)
    if not task:
        return
    try:
        url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{task['uuid']}"
        response = requests.get(url)
        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("响应格式不正确或不包含 Status 字段")

        cur_status = data.get("Status")
        cur_updated = data.get("LastUpdated")
        reviewers_data = data.get("ReviewEvents", [])
        current_review_hash = generate_hash(reviewers_data)

        reviewer_map = {}
        for ev in reviewers_data:
            reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)

        status_str = status_description(cur_status, reviewer_map)
        write_log(task['recipient_email'], task['uuid'],
                  f"🔍 定时检查，状态: {status_str}，更新时间: {display_time(cur_updated).strftime('%Y-%m-%d %H:%M:%S')}", threshold=MAX_LOG_LINES)

        should_notify = False
        reasons = []
        if task['last_status'] != cur_status:
            should_notify = True
            reasons.append("状态码变化")
        if task['last_updated'] != cur_updated:
            should_notify = True
            reasons.append("更新时间变化")
        if task.get("review_hash") != current_review_hash:
            should_notify = True
            reasons.append("审稿人信息变化")

        revision_max = max([r.get("Revision", 0) for r in reviewers_data], default=0)
        current_round = [r for r in reviewers_data if r.get("Revision", 0) == revision_max]
        reviewer_ids = set(ev["Id"] for ev in current_round if ev["Event"] == "Reviewer Invited")
        completed_ids = set(
            ev["Id"] for ev in current_round if ev["Event"] in {"Review Completed", "Declined", "Unassigned"})
        is_rrc = reviewer_ids and reviewer_ids.issubset(completed_ids)
        if is_rrc:
            should_notify = True
            reasons.append("本轮所有审稿人已完成（RRC）")

        if should_notify:
            reviewer_map = {}
            for ev in reviewers_data:
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)

            status_str = status_description(cur_status, reviewer_map)
            subject = "📢 稿件状态更新通知"
            body = (
                f"📄 <b>稿件标题：</b>{data.get('ManuscriptTitle')}\n"
                f"🔢 <b>稿件编号：</b>{data.get('PubdNumber')}\n"
                f"📚 <b>投稿期刊：</b>{data.get('JournalName')}\n"
                f"🧑‍🔬 <b>第一作者：</b>{data.get('FirstAuthor')}\n"
                f"📧 <b>通讯作者：</b>{data.get('CorrespondingAuthor')}\n\n"
                f"📌 <b>当前状态：</b>{status_str}\n"
                f"🕓 <b>最近更新时间：</b>{display_time(cur_updated).strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🔍 <b>变动原因：</b>{'、'.join(reasons)}\n\n"
                f"⏰ <b>检查时间：</b>{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')} (北京时间)\n"
                f"📡 本系统将持续每 {task['interval']} 分钟自动检测一次，如有变化将再次通知。"
            )

            send_email(subject, body, task['sender_email'], task['password'], task['recipient_email'], task['uuid'])
            write_log(task['recipient_email'], task['uuid'], f"📢 通知发送，原因: {'、'.join(reasons)}",
                      threshold=MAX_LOG_LINES)
            task['last_status'] = cur_status
            task['last_updated'] = cur_updated
            task['review_hash'] = current_review_hash
        task['fail_count'] = 0

    except Exception as e:
        # 初始化或更新失败计数器
        task.setdefault('fail_count', 0)
        task['fail_count'] += 1

        if task['fail_count'] >= 3:
            write_log(task['recipient_email'], task['uuid'], f"❌ 连续失败 {task['fail_count']} 次，任务终止: {e}",
                      threshold=MAX_LOG_LINES)
            scheduler.remove_job(task_id)
            recipient = task['recipient_email']
            del monitor_tasks[task_id]

            # 删除日志文件
            filename = f"{recipient}_log.txt"
            if os.path.exists(filename):
                os.remove(filename)
        else:
            write_log(task['recipient_email'], task['uuid'], f"⚠️ 第 {task['fail_count']} 次检查失败（未终止）: {e}",
                      threshold=MAX_LOG_LINES)


# 接下来将继续追加主页、监控任务提交、查询页面等 Flask 路由和 HTML 模板字符串

# （前略）前面定义的工具函数和定时任务略去

# ============ 监控任务主界面 ============
@app.route('/', methods=['GET', 'POST'])
@app.route('/', methods=['GET', 'POST'])
def index():
    global MAX_TASKS
    form = MonitorForm()
    can_submit = len(monitor_tasks) < MAX_TASKS
    if form.validate_on_submit() and can_submit:
        if not form.sender_email.data.endswith('@qq.com'):
            return render_template_string(TEMPLATE, form=form, error="❌ 当前仅支持 QQ 邮箱",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)
        task_id = f"task_{len(monitor_tasks) + 1}"
        raw_uuid = form.uuid.data.strip()
        if "uuid=" in raw_uuid:
            import urllib.parse
            parsed = urllib.parse.urlparse(raw_uuid)
            params = urllib.parse.parse_qs(parsed.query)
            uuid = params.get("uuid", [""])[0]
        else:
            uuid = raw_uuid
        for task in monitor_tasks.values():
            if task['recipient_email'] == form.recipient_email.data and task['uuid'] == uuid:
                flash("⚠️ 当前收件人已经在监控该稿件，若要重新提交请联系管理员！", "warning")
                return redirect(url_for('index'))
        if task_id in monitor_tasks:
            flash(f"⚠️ 该稿件已在监控中，若要重新提交请联系管理员！", "warning")
            return redirect(url_for('index'))
        task = {
            'sender_email': form.sender_email.data,
            'password': form.password.data,
            'recipient_email': form.recipient_email.data,
            'uuid': uuid,
            'interval': form.interval.data,
            'last_status': None,
            'last_updated': None,
            'review_hash': None,
            'fail_count': 0  # ✅ 初始化失败计数器
        }
        try:
            url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{task['uuid']}"
            response = requests.get(url)
            try:
                data = response.json()
            except ValueError:
                raise ValueError("无效的 UUID 或服务器未返回有效信息。")
            task['last_status'] = data.get("Status")
            task['last_updated'] = data.get("LastUpdated")
            task["review_hash"] = generate_hash(data.get("ReviewEvents", []))

            subject = "📡 稿件监控已启动"
            reviewer_map = {}
            for ev in data.get("ReviewEvents", []):
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)
            status_str = status_description(data.get("Status"), reviewer_map)
            body = f"""
                        <b>📡 您的稿件监控任务已成功启动！</b><br><br>
                        📄 <b>稿件标题：</b>{data.get('ManuscriptTitle')}<br>
                        🔢 <b>稿件编号：</b>{data.get('PubdNumber')}<br>
                        📚 <b>投稿期刊：</b>{data.get('JournalName')}<br>
                        🧑‍🔬 <b>第一作者：</b>{data.get('FirstAuthor')}<br>
                        📧 <b>通讯作者：</b>{data.get('CorrespondingAuthor')}<br><br>
                        📌 <b>当前状态：</b>{status_str}<br>
                        🕓 <b>最近更新时间：</b>{display_time(data.get('LastUpdated')).strftime('%Y-%m-%d %H:%M:%S')}<br>
                        ⏰ <b>监控频率：</b>每 {task['interval']} 分钟自动检测一次<br><br>

                        ✅ 系统将在检测到以下任意变动时发送通知：<br>
                        - 状态码变化<br>
                        - 更新时间变化<br>
                        - 最新一轮审稿人全部完成（RRC）<br><br>

                        📅 启动时间：{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')}（北京时间）<br>
                        🔒 如需关闭，请联系管理员或在平台页面操作。
                        """
            if not send_email(subject, body, task['sender_email'], task['password'], task['recipient_email'],
                              task['uuid']):
                return render_template_string(TEMPLATE, form=form, error="❌ 邮件发送失败，请检查发件邮箱或授权码。",
                                              monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                              max_tasks=MAX_TASKS, can_submit=can_submit)
            reviewer_map = {}
            for ev in data.get("ReviewEvents", []):
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)
            status_str = status_description(data.get("Status"), reviewer_map)
            write_log(task['recipient_email'], task['uuid'],
                      f"📡 监控已启动，状态: {status_description(data.get('Status'), reviewer_map)}",
                      threshold=MAX_LOG_LINES)
        except Exception as e:
            write_log(task['recipient_email'], task['uuid'], f"❌ 启动失败: {e}", threshold=MAX_LOG_LINES)
            return render_template_string(TEMPLATE, form=form, error=f"❌ 启动失败: {e}",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)

        monitor_tasks[task_id] = task
        scheduler.add_job(id=task_id, func=check_status, args=[task_id],
                          trigger='interval', minutes=task['interval'], max_instances=3, coalesce=True)
        flash("✅ 监测已开始", "success")
        return redirect(url_for('index'))

    return render_template_string(TEMPLATE, form=form, monitor_tasks=monitor_tasks,
                                  admin=session.get("admin", False), max_tasks=MAX_TASKS,
                                  can_submit=can_submit, error=None)


# 删除监控任务
@app.route('/delete/<task_id>', methods=['POST'])
def delete_task(task_id):
    if session.get("admin") and task_id in monitor_tasks:
        task = monitor_tasks[task_id]
        recipient_email = task['recipient_email']
        masked = recipient_email[:3] + '***' + recipient_email[-3:]
        log_filename = f"{masked}_{task['uuid']}_log.txt"

        # 停止定时任务
        scheduler.remove_job(task_id)

        # 写入删除日志（需加 uuid）
        write_log(recipient_email, task['uuid'], f"🗑️ 管理员删除任务 {task_id}，日志文件即将删除",
                  threshold=MAX_LOG_LINES)

        # 删除日志文件（如果存在）
        if os.path.exists(log_filename):
            os.remove(log_filename)

        # 删除监控任务
        del monitor_tasks[task_id]

    return redirect(url_for('index'))


# 管理员登录
@app.route('/admin', methods=['GET', 'POST'])
def admin():
    form = LoginForm()
    if form.validate_on_submit():
        if form.username.data == ADMIN_USERNAME and form.password.data == ADMIN_PASSWORD:
            session['admin'] = True
            return redirect(url_for('admin_config'))
    return render_template_string(ADMIN_TEMPLATE, form=form)


@app.route('/admin/config', methods=['GET', 'POST'])
def admin_config():
    global MAX_TASKS, MAX_LOG_LINES
    if not session.get("admin"):
        return redirect(url_for('admin'))
    form = ConfigForm()
    if form.validate_on_submit():
        MAX_TASKS = form.max_tasks.data
        MAX_LOG_LINES = form.log_lines.data
        flash("✅ 设置已更新", "success")
        return redirect(url_for('index'))  # 更新后返回主页面
    form.max_tasks.data = MAX_TASKS
    form.log_lines.data = MAX_LOG_LINES
    return render_template_string(CONFIG_TEMPLATE, form=form, current=MAX_TASKS, current_log_lines=MAX_LOG_LINES)


@app.route('/logout')
def logout():
    session.pop("admin", None)
    flash("您已成功退出管理员模式。", "info")
    return redirect(url_for('index'))


@app.route('/logs/<masked_email>/<uuid>')
def view_log(masked_email, uuid):
    if not session.get("admin"):
        return redirect(url_for('admin'))

    # 从 monitor_tasks 中查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        masked = task['recipient_email'][:3] + '***' + task['recipient_email'][-3:]
        if masked == masked_email:
            real_email = task['recipient_email']
            break

    if not real_email:
        return f"⚠️ 无法找到对应邮箱: {masked_email}"

    masked = real_email[:3] + '***' + real_email[-3:]
    filename = f"{masked}_{uuid}_log.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        content = ''.join(reversed(lines))  # 最新在上方显示
    except FileNotFoundError:
        content = "⚠️ 无日志文件或尚未生成。"

    return render_template_string(LOG_TEMPLATE, masked_email=masked_email, content=content, uuid=uuid)


@app.route('/clear_log/<masked_email>/<uuid>', methods=['POST'])
def clear_log(masked_email, uuid):
    if not session.get("admin"):
        return redirect(url_for('admin'))

    # 查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        masked = task['recipient_email'][:3] + '***' + task['recipient_email'][-3:]
        if masked == masked_email:
            real_email = task['recipient_email']
            break

    if not real_email:
        flash("⚠️ 无法识别的邮箱标识", "warning")
        return redirect(url_for('index'))

    masked = real_email[:3] + '***' + real_email[-3:]
    filename = f"{masked}_{uuid}_log.txt"

    try:
        if os.path.exists(filename):
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("")
        flash("✅ 日志已清空", "success")
    except Exception as e:
        flash(f"❌ 日志清空失败: {e}", "danger")

    return redirect(url_for('view_log', masked_email=masked_email, uuid=uuid))


# 下一步将追加 query 查询视图与其模板字符串

# （前略）

# ============ UUID 查询入口与结果展示 ============
@app.route('/query', methods=['GET', 'POST'])
def query():
    if request.method == "POST":
        raw_input = request.form.get("uuid", "").strip()
        # 自动提取 uuid 参数
        if "uuid=" in raw_input:
            import urllib.parse
            parsed = urllib.parse.urlparse(raw_input)
            params = urllib.parse.parse_qs(parsed.query)
            uuid = params.get("uuid", [""])[0]
        else:
            uuid = raw_input
        if uuid:
            return redirect(f"/result?uuid={uuid}")
    return render_template_string(QUERY_INPUT_TEMPLATE)


@app.route('/result')
def result():
    raw_uuid = request.args.get("uuid", "").strip()
    if "uuid=" in raw_uuid:
        import urllib.parse
        parsed = urllib.parse.urlparse(raw_uuid)
        params = urllib.parse.parse_qs(parsed.query)
        uuid = params.get("uuid", [""])[0]
    else:
        uuid = raw_uuid
    api_url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{uuid}"
    try:
        response = requests.get(api_url, timeout=10)
        if response.status_code != 200:
            raise ValueError("无效响应")

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("数据格式错误")

        reviewers = []
        reviewer_map = {}
        for ev in data.get("ReviewEvents", []):
            rid = ev["Id"]
            rev = ev["Revision"]
            reviewer_map.setdefault((rid, rev), []).append(ev)

        invited_count = len(set(k for k in reviewer_map))
        accepted_count = 0
        
        # 使用 display_time 函数计算提交时间和最近更新时间
        submission_date = display_time(data.get("SubmissionDate"))
        last_updated = display_time(data.get("LastUpdated"))
        status_code = str(data.get("Status")).strip()
        if status_code in ["9", "39"]: 
            elapsed = last_updated - submission_date
            elapsed_days = elapsed.days
            elapsed_hours = elapsed.seconds // 3600
            elapsed_minutes = (elapsed.seconds % 3600) // 60
            elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（已完结）"
        else:
            current_time = datetime.now(timezone.utc)
            elapsed = current_time - submission_date
            elapsed_days = elapsed.days
            elapsed_hours = elapsed.seconds // 3600
            elapsed_minutes = (elapsed.seconds % 3600) // 60
            elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（进行中）"
        
        for (rid, rev), actions in reviewer_map.items():
            invite_time = accept_time = complete_time = None
            for a in actions:
                if a["Event"] == "REVIEWER_INVITED":
                    invite_time = a["Date"]
                elif a["Event"] == "REVIEWER_ACCEPTED":
                    accept_time = a["Date"]
                elif a["Event"] == "REVIEWER_COMPLETED":
                    complete_time = a["Date"]

            if not accept_time:
                continue
            accepted_count += 1

            # 接受邀请耗时
            dd, ss = days_between(invite_time, accept_time) if invite_time else (0, 0)

            # 展示时间（+5小时）
            accept_display = display_time(accept_time)
            complete_display = display_time(complete_time) if complete_time else None

            # 审稿耗时（完成 - 接受）
            if complete_display:
                delta = complete_display - accept_display
                ed = delta.days
                es = delta.seconds
                review_time_str = f"{ed}天 {es // 3600}小时 {es % 3600 // 60}分 {es % 60}秒"
            else:
                review_time_str = "尚未完成"

            # 当前耗时（现在 - 接受展示时间）
            now = datetime.now(CST)
            elapsed = now - accept_display
            elapsed_d = elapsed.days
            elapsed_s = elapsed.seconds
            review_elapsed_str = None if complete_time else f"{elapsed_d}天 {elapsed_s // 3600}小时 {elapsed_s % 3600 // 60}分 {elapsed_s % 60}秒"


            reviewers.append({
                "id": rid,
                "revision": rev,
                "invite_time": display_time(invite_time).strftime('%Y-%m-%d %H:%M:%S') if invite_time else "—",
                "accept_time": accept_display.strftime('%Y-%m-%d %H:%M:%S'),
                "complete_time": complete_display.strftime('%Y-%m-%d %H:%M:%S') if complete_display else "未完成",
                "duration": f"{dd}天 {ss // 3600}小时 {ss % 3600 // 60}分 {ss % 60}秒",
                "review_time": review_time_str,
                "review_elapsed": review_elapsed_str
            })

        group_stats = {}
        for (rid, rev), actions in reviewer_map.items():
            invited = any(a["Event"] == "REVIEWER_INVITED" for a in actions)
            accepted = any(a["Event"] == "REVIEWER_ACCEPTED" for a in actions)
            completed = any(a["Event"] == "REVIEWER_COMPLETED" for a in actions)
            group_stats.setdefault(rev, {"invited": 0, "accepted": 0, "completed": 0})
            if invited:
                group_stats[rev]["invited"] += 1
            if accepted:
                group_stats[rev]["accepted"] += 1
            if completed:
                group_stats[rev]["completed"] += 1

        status_str = status_description(data.get("Status"), reviewer_map)

        return render_template_string(QUERY_RESULT_TEMPLATE,
                                      title=data.get("ManuscriptTitle"),
                                      pubd_number=data.get("PubdNumber"),
                                      journal=data.get("JournalName"),
                                      first_author=data.get("FirstAuthor"),
                                      corresponding_author=data.get("CorrespondingAuthor"),
                                      uuid=uuid,
                                      status=status_str,
                                      elapsed=elapsed_str,
                                      updated=display_time(data.get("LastUpdated")).strftime('%Y-%m-%d %H:%M:%S'),
                                      submitted=display_time(data.get("SubmissionDate")).strftime('%Y-%m-%d %H:%M:%S'),
                                      invited_count=invited_count,
                                      accepted_count=accepted_count,
                                      reviewers=reviewers,
                                      group_stats=group_stats,
                                      query_time=datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S'))
    except Exception:
        return render_template_string("""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>查询失败</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body {
                    background-color: #f8f9fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .card {
                    max-width: 500px;
                    width: 100%;
                    padding: 2rem;
                    border-radius: 12px;
                    box-shadow: 0 0 12px rgba(0, 0, 0, 0.08);
                    background-color: white;
                }
                .highlight {
                    color: #d63384;
                    font-weight: bold;
                }
            </style>
        </head>
            <body>
                <div class="card text-center">
                    <h4 class="mb-4 text-danger">❌ 查询失败</h4>
                    <p class="text-muted">
                        您输入的内容不是有效的 UUID 或 Track 链接。<br>
                        请确保输入的是 <strong>稿件状态链接</strong> 或其末尾的 <strong>UUID</strong>，而非稿件编号或投稿编号。<br>
                        正确格式示例：<br>
                        <code><span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code>
                        或<br>
                        <code>https://.../tracker?uuid=<span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code><br><br>
                        📬 提醒：该链接通常在稿件<strong>第一次送审后</strong>由系统发送至<strong>通讯作者邮箱</strong>，请注意查收相关邮件。
                    </p>
                    <div class="d-grid gap-2 mt-4">
                        <a href="/query" class="btn btn-outline-primary btn-lg">🔙 返回重新输入</a>
                        <a href="/" class="btn btn-outline-secondary btn-lg">📡 返回监控主页</a>
                    </div>
                    <div class="text-center text-muted mt-5 mb-3">
                            © 2025 小红书博主 <strong>ZeroFive</strong> 版权所有
                    </div>
                </div>
            </body>
        </html>
        """)


# 接下来将追加 QUERY_INPUT_TEMPLATE, QUERY_RESULT_TEMPLATE, TEMPLATE 等 HTML 模板

# （前略）前面为主程序、工具函数与所有视图函数定义

# ============ 所有 HTML 模板字符串 ============

TEMPLATE = '''
<!DOCTYPE html>
<html><head>
    <title>稿件监控平台</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 900px;
        }
        .form-control, .btn { 
            font-size: 1rem;
            border-radius: 10px;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
            font-size: 2.2rem;
        }
        .task-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            border-radius: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .task-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .nav-buttons {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head><body>
<div class="container my-4">
    <div class="main-container p-4">
        <h2 class="page-title mb-4 text-center">
            <i class="fas fa-file-alt me-2"></i>Elsevier 稿件监控平台
        </h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-lg-6">
                <form method="post">{{ form.hidden_tag() }}
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-envelope me-2"></i>{{ form.sender_email.label.text }}</label>
                        {{ form.sender_email(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-key me-2"></i>{{ form.password.label.text }}</label>
                        {{ form.password(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-inbox me-2"></i>{{ form.recipient_email.label.text }}</label>
                        {{ form.recipient_email(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-link me-2"></i>{{ form.uuid.label.text }}</label>
                        {{ form.uuid(class="form-control") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-clock me-2"></i>{{ form.interval.label.text }}</label>
                        {{ form.interval(class="form-control") }}
                    </div>
                    {{ form.submit(class="btn btn-success w-100", disabled=not can_submit) }}
                </form>

                {% if error %}
                  <div class="alert alert-danger mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                  </div>
                {% endif %}
            </div>
            
            <div class="col-lg-6">
                <h4 class="mt-4 mt-lg-0 mb-3">
                    <i class="fas fa-tasks me-2"></i>当前监控任务（{{ monitor_tasks|length }}/{{ max_tasks }}）
                </h4>
                <div class="task-list" style="max-height: 400px; overflow-y: auto;">
                    {% for task_id, task in monitor_tasks.items() %}
                    <div class="task-item p-3 mb-2">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="mb-2 mb-md-0">
                                <div><i class="fas fa-user me-2"></i><strong>收件人:</strong> {{ task.recipient_email[:3] + '***' + task.recipient_email[-3:] }}</div>
                                <div><i class="fas fa-stopwatch me-2"></i><strong>间隔:</strong> {{ task.interval }} 分钟</div>
                            </div>
                            {% if admin %}
                                <div class="d-flex gap-2">
                                    <button class="btn btn-danger btn-sm" onclick="if(confirm('确认删除该监控任务吗？')) { document.getElementById('deleteForm{{ loop.index }}').submit(); }">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <form id="deleteForm{{ loop.index }}" method="post" action="{{ url_for('delete_task', task_id=task_id) }}" style="display: none;"></form>
                                    <a class="btn btn-outline-info btn-sm" href="{{ url_for('view_log', masked_email=task.recipient_email[:3] + '***' + task.recipient_email[-3:], uuid=task.uuid) }}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                        <p>暂无监控任务</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="nav-buttons text-center">
            <div class="d-flex flex-column flex-sm-row justify-content-center gap-3">
                {% if admin %}
                    <a href="{{ url_for('admin_config') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>设置
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>退出管理员
                    </a>
                {% else %}
                    <a href="{{ url_for('admin') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-lock me-2"></i>管理员登录
                    </a>
                {% endif %}
                <a href="{{ url_for('query') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>查询稿件详细状态
                </a>
            </div>
        </div>
        
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body></html>
'''

ADMIN_TEMPLATE = '''
<!DOCTYPE html>
<html><head>
    <title>管理员登录</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
        }
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .lock-icon {
            font-size: 3rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    </style>
</head><body>
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
    <div class="login-container shadow" style="width: 100%; max-width: 450px;">
        <div class="text-center mb-4">
            <i class="fas fa-shield-alt lock-icon"></i>
            <h3 class="page-title mt-3">管理员登录</h3>
            <p class="text-muted">请输入管理员凭据</p>
        </div>
        <form method="post">{{ form.hidden_tag() }}
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-user text-primary"></i>
                    </span>
                    {{ form.username(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="管理员账号") }}
                </div>
            </div>
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-lock text-primary"></i>
                    </span>
                    {{ form.password(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="密码") }}
                </div>
            </div>
            <div class="d-grid">
                {{ form.submit(class="btn btn-primary btn-lg") }}
            </div>
        </form>
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
</body></html>
'''

CONFIG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>最大任务配置</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .config-card {
            max-width: 500px;
            margin: 40px auto;
            background-color: #ffffff;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }
        .config-title {
            font-size: 1.5rem;
            color: #0d6efd;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="config-card">
        <div class="config-title">⚙️ 设置</div>
        <form method="post">
            {{ form.hidden_tag() }}
            <div class="mb-3 text-center">
                当前最大任务数：<strong class="text-success">{{ current }}</strong>
            </div>
            <div class="mb-3">
                {{ form.max_tasks.label(class="form-label") }}
                {{ form.max_tasks(class="form-control") }}
            </div>
            <div class="mb-3 text-center">
                当前每个日志最多保留：<strong class="text-success">{{ current_log_lines }}</strong> 条
            </div>
            <div class="mb-3">
                {{ form.log_lines.label(class="form-label") }}
                {{ form.log_lines(class="form-control") }}
            </div>
            <div class="d-grid gap-2">
                {{ form.submit(class="btn btn-success") }}
            </div>
        </form>
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-sm w-50 me-2">⬅️ 返回主页</a>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm w-50">退出管理员</a>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

LOG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>查看日志 - {{ masked_email }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f4f6f8; }
        .log-box {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 1rem;
            font-size: 0.95rem;
            line-height: 1.6;
            overflow-wrap: break-word;
            box-shadow: 0 0 8px rgba(0,0,0,0.05);
        }
        .log-entry {
            padding: 0.75rem;
            border-left: 4px solid #dee2e6;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .highlight {
            background-color: #e6f4ea;
            border-left-color: #28a745 !important;
        }
        .log-time {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .log-content {
            font-weight: 500;
            word-break: break-word;
        }
        @media (max-width: 576px) {
            .log-entry {
                padding: 0.5rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    <div class="container mb-3">
    {% for category, message in messages %}
      <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endfor %}
    </div>
  {% endif %}
{% endwith %}
<div class="container my-4">
    <div class="d-flex justify-content-center gap-2 mb-3 flex-wrap">
    <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-lg">⬅️ 返回监控主页</a>
        <!-- 触发弹窗按钮 -->
    <button class="btn btn-outline-danger btn-lg" data-bs-toggle="modal" data-bs-target="#clearLogModal">🗑️ 清空日志</button>

    <!-- 模态框 Modal -->
    <div class="modal fade" id="clearLogModal" tabindex="-1" aria-labelledby="clearLogModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title text-danger" id="clearLogModalLabel">⚠️ 确认清空日志</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
          </div>
          <div class="modal-body">
            确定要清空该日志文件吗？此操作不可恢复。
          </div>
          <div class="modal-footer">
            <form method="post" action="{{ url_for('clear_log', masked_email=masked_email, uuid=uuid) }}">
              <button type="submit" class="btn btn-danger">确认清空</button>
            </form>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          </div>
        </div>
      </div>
    </div>

</div>
    <h3 class="mb-3 text-primary text-center">📜 日志查看：{{ masked_email }} | UUID: {{ uuid }}</h3>
    <div class="log-box mb-4">
        {% for line in content.splitlines() %}
            {% set time = line.split("]")[0][1:] %}
            {% set message = line.split("]")[1].strip() %}
            {% set important = '📢' in message or '📧' in message %}
            <div class="log-entry {% if important %}highlight{% endif %}">
                <div class="log-time">🕒 {{ time }}</div>
                <div class="log-content">{{ message }}</div>
            </div>
        {% endfor %}
    </div>
    <div class="text-center text-muted mt-5 mb-3">
        © 2025 小红书博主 <strong>ZeroFive</strong> 版权所有
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# QUERY_INPUT_TEMPLATE 和 QUERY_RESULT_TEMPLATE 请在下一个步骤中继续追加
# （前略）

# ============ 查询 UUID 输入页模板 ============
QUERY_INPUT_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>稿件状态查询</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .query-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
        }
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .icon-large {
            font-size: 4rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
    </style>
    <script>
        window.onload = function() {
            const uuidInput = document.querySelector("input[name='uuid']");
            const lastUuid = localStorage.getItem("last_uuid");
            if (lastUuid && uuidInput) {
                uuidInput.value = lastUuid;
            }
            document.querySelector("form").addEventListener("submit", function() {
                localStorage.setItem("last_uuid", uuidInput.value);
            });
        }
    </script>
</head>
<body>
<div class="container">
    <div class="query-container shadow" style="max-width: 600px; margin: 0 auto;">
        <div class="text-center mb-4">
            <i class="fas fa-search icon-large"></i>
            <h3 class="page-title">稿件状态查询</h3>
            <p class="text-muted">输入UUID或Track链接快速查询稿件状态</p>
        </div>
        
        <form method="POST">
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-link text-primary"></i>
                    </span>
                    <input type="text" 
                           name="uuid" 
                           class="form-control border-start-0" 
                           style="border-radius: 0 15px 15px 0;"
                           placeholder="请输入UUID或Track链接" 
                           required>
                </div>
            </div>
            
            <div class="d-grid gap-3 mb-3">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>开始查询
                </button>
                <a href="/" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-home me-2"></i>返回监控平台
                </a>
            </div>
        </form>
        
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
</body>
</html>
'''

# ============ 查询结果展示模板 ============
QUERY_RESULT_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>稿件状态监控</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <style>
        body { background-color: #f5f7fa; }
        .badge-status { font-size: 1rem; }
        .badge-submitted { background-color: #0d6efd; }
        .badge-assigned { background-color: #6610f2; }
        .badge-review { background-color: #fd7e14; }
        .badge-rrc { background-color: #20c997; }
        .badge-dip { background-color: #ffc107; }
        .badge-rev { background-color: #6f42c1; }
        .badge-acc { background-color: #198754; }
        .badge-rej { background-color: #dc3545; }
        .review-card { border-left: 5px solid #0d6efd; }
        .section-title {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
<div class="container my-5">
    <h1 class="text-primary mb-4">📄 Elsevier稿件状态自助查询</h1>

    <div class="card shadow-sm p-4 mb-4">
        <h4 class="section-title">📌 稿件基本信息</h4>
        <p><strong>标题：</strong>{{ title }}</p>
        <p><strong>编号：</strong>{{ pubd_number }}</p>
        <p><strong>期刊：</strong>{{ journal }}</p>
        <p><strong>第一作者：</strong>{{ first_author }}</p>
        <p><strong>通讯作者：</strong>{{ corresponding_author }}</p>
        <p><strong>当前状态：</strong>
            {% set badge_color = "badge-submitted" %}
            {% if "已提交" in status %} {% set badge_color = "badge-submitted" %}
            {% elif "已分配" in status %} {% set badge_color = "badge-assigned" %}
            {% elif "审稿中" in status %} {% set badge_color = "badge-review" %}
            {% elif "等待决定" in status %} {% set badge_color = "badge-rrc" %}
            {% elif "评估中" in status %} {% set badge_color = "badge-dip" %}
            {% elif "修订" in status %} {% set badge_color = "badge-rev" %}
            {% elif "已接受" in status %} {% set badge_color = "badge-acc" %}
            {% elif "已拒稿" in status %} {% set badge_color = "badge-rej" %}
            {% endif %}
            <span class="badge badge-status {{ badge_color }}">{{ status }}</span>
        </p>
        <p><strong>提交时间：</strong>{{ submitted }}</p>
        <p><strong>稿件耗时：</strong>{{ elapsed }}</p>
        <p><strong>最近更新时间：</strong>{{ updated }}</p>
        <p><strong>已邀请审稿人：</strong>{{ invited_count }} 位</p>
        <p><strong>已接受邀请：</strong>{{ accepted_count }} 位</p>
    </div>

    <h4 class="section-title">👨‍⚖ 审稿人状态详情</h4>
    {% set grouped = {} %}
    {% for r in reviewers %}
        {% set _ = grouped.setdefault(r.revision, []).append(r) %}
    {% endfor %}
    <div class="accordion" id="reviewAccordion">
    {% for rev, group in grouped.items() %}
    <div class="accordion-item">
        <h2 class="accordion-header" id="heading{{ rev }}">
            <button class="accordion-button {% if rev != grouped|length - 1 %}collapsed{% endif %}" 
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target="#collapse{{ rev }}" 
                    aria-expanded="{% if rev == grouped|length - 1 %}true{% else %}false{% endif %}" 
                    aria-controls="collapse{{ rev }}"
                    style="">
                第 {{ rev + 1 }} 轮（邀请 {{ group_stats[rev].invited }} 人，接受 {{ group_stats[rev].accepted }} 人，完成 {{ group_stats[rev].completed }} 人）
            </button>
        </h2>
        <div id="collapse{{ rev }}" class="accordion-collapse collapse {% if rev == grouped|length - 1 %}show{% endif %}" 
             aria-labelledby="heading{{ rev }}" 
             data-bs-parent="#reviewAccordion">
            <div class="accordion-body">
                <div class="row">
                {% for r in group %}
                    <div class="col-md-6">
                        <div class="card shadow-sm mb-4 review-card">
                            <div class="card-body">
                                <h5 class="card-title">审稿人 ID: {{ r.id }}</h5>
                                <p><strong>📨 邀请时间：</strong> {{ r.invite_time }}</p>
                                <p><strong>✅ 接受时间：</strong> {{ r.accept_time }}</p>
                                <p><strong>📘 完成时间：</strong> {{ r.complete_time }}</p>
                                <p><strong>⏱ 接受耗时：</strong> {{ r.duration }}</p>
                                <p><strong>📗 审稿耗时：</strong> {{ r.review_time }}</p>
                                {% if r.review_elapsed %}
                                    <p><strong>📙 已审稿时长：</strong> {{ r.review_elapsed }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
                </div>
            </div>
        </div>
    </div>
{% endfor %}
    </div>

    <div class="text-center text-muted mt-4">
        页面数据为实时获取 | 所有时间均为北京时间<br>
        🕒 查询时间：{{ query_time }}
    </div>
    <div class="text-center mt-4">
    <a href="/query" class="btn btn-outline-primary">🔄 返回继续查询</a>
    <a href="/" class="btn btn-outline-secondary ms-2">📡 返回监控平台</a>
</div>
    <!-- ✅ 版权信息 -->
    <div class="text-center text-muted mt-5 mb-3">
        © 2025 小红书博主 <strong>ZeroFive</strong> 版权所有
    </div>
</div>
</body>
</html>
'''

# ===== 启动应用 =====
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1001, debug=False)