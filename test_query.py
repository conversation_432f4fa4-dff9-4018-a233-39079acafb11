import requests
from datetime import datetime, timezone, timedelta

# 模拟你的查询代码
CST = timezone(timedelta(hours=8))

def display_time(ts):
    return datetime.fromtimestamp(ts, tz=timezone.utc).astimezone(CST) + timedelta(hours=5)

def days_between(ts1, ts2=None):
    dt1 = datetime.fromtimestamp(ts1, tz=timezone.utc)
    dt2 = datetime.now(timezone.utc) if ts2 is None else datetime.fromtimestamp(ts2, tz=timezone.utc)
    delta = dt2 - dt1
    return delta.days, delta.seconds

def status_description(code, reviewer_map=None):
    mapping = {
        "1": "Submitted - 已提交",
        "2": "Editor Assigned - 编辑已分配", 
        "3": "Under Review - 正在审稿中",
        "4": "Reviews Completed (RRC) - 审稿已完成",
        "5": "Decision in Progress - 编辑评估中",
        "6": "Decision in Process - 决定中",
        "7": "Revision Required - 需要修订",
        "8": "With Editor - 编辑评估中",
        "9": "Accepted - 已接受",
        "39": "Rejected - 已拒稿",
        "29": "Decision in Progress - 编辑评估中",
    }
    return mapping.get(str(code), f"未知状态（{code}）")

# 测试你的UUID
uuid = '016cff7e-51d9-43de-958e-677c0e61dc1d'
api_url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{uuid}"

try:
    response = requests.get(api_url, timeout=10)
    if response.status_code != 200:
        raise ValueError("无效响应")

    data = response.json()
    if not isinstance(data, dict) or "Status" not in data:
        raise ValueError("数据格式错误")

    # 先计算总体时间信息（移到循环外部）
    submission_date = display_time(data.get("SubmissionDate"))
    last_updated = display_time(data.get("LastUpdated"))
    status_code = str(data.get("Status")).strip()
    if status_code in ["9", "39"]: 
        elapsed = last_updated - submission_date
        elapsed_days = elapsed.days
        elapsed_hours = elapsed.seconds // 3600
        elapsed_minutes = (elapsed.seconds % 3600) // 60
        elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（已完结）"
    else:
        current_time = datetime.now(timezone.utc)
        elapsed = current_time - submission_date
        elapsed_days = elapsed.days
        elapsed_hours = elapsed.seconds // 3600
        elapsed_minutes = (elapsed.seconds % 3600) // 60
        elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（进行中）"

    print("✅ 成功计算时间信息:")
    print(f"   提交时间: {submission_date}")
    print(f"   最后更新: {last_updated}")
    print(f"   耗时: {elapsed_str}")
    
    # 处理审稿人信息
    reviewers = []
    reviewer_map = {}
    for ev in data.get("ReviewEvents", []):
        rid = ev["Id"]
        rev = ev["Revision"]
        reviewer_map.setdefault((rid, rev), []).append(ev)

    invited_count = len(set(k for k in reviewer_map))
    accepted_count = 0
    
    print(f"\n📊 审稿人统计:")
    print(f"   邀请: {invited_count} 人")
    
    for (rid, rev), actions in reviewer_map.items():
        invite_time = accept_time = complete_time = None
        for a in actions:
            if a["Event"] == "REVIEWER_INVITED":
                invite_time = a["Date"]
            elif a["Event"] == "REVIEWER_ACCEPTED":
                accept_time = a["Date"]
            elif a["Event"] == "REVIEWER_COMPLETED":
                complete_time = a["Date"]

        if not accept_time:
            print(f"   审稿人 {rid}: 仅被邀请，未接受")
            continue
        accepted_count += 1
        print(f"   审稿人 {rid}: 已接受邀请")

    print(f"   接受: {accepted_count} 人")
    
    status_str = status_description(data.get("Status"), reviewer_map)
    print(f"\n📌 状态: {status_str}")
    print(f"🎉 查询处理成功完成！")

except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
