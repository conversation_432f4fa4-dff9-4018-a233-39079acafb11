# 优化后的 Flask 应用 - Elsevier 稿件监控平台

from flask import Flask, render_template_string, request, redirect, url_for, session, flash
from flask_apscheduler import APScheduler
from flask_wtf import FlaskForm
from wtforms import String<PERSON>ield, PasswordField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Email, NumberRange
import requests
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timezone, timedelta
import json
import hashlib
import os
import urllib.parse
import logging

# ============ 应用配置 ============
app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.environ.get('SECRET_KEY', 'your_secret_key_change_in_production'),
    'WTF_CSRF_ENABLED': True,
    'WTF_CSRF_TIME_LIMIT': 3600
})

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化调度器
scheduler = APScheduler()
scheduler.init_app(app)
scheduler.start()

# ============ 配置常量 ============
CST = timezone(timedelta(hours=8))  # 北京时间
monitor_tasks = {}

# 从环境变量读取配置，提供默认值
MAX_TASKS = int(os.environ.get('MAX_TASKS', 10))
MAX_LOG_LINES = int(os.environ.get('MAX_LOG_LINES', 50))
ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', "3313637051")
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', "aidi+6898")
API_BASE_URL = os.environ.get('API_BASE_URL', "https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker")

# 配置验证
def validate_config():
    """验证应用配置"""
    if app.config['SECRET_KEY'] == 'your_secret_key_change_in_production':
        logger.warning("⚠️ 使用默认SECRET_KEY，生产环境请修改")

    if ADMIN_PASSWORD == "aidi+6898":
        logger.warning("⚠️ 使用默认管理员密码，建议修改")

    logger.info(f"✅ 配置加载完成 - 最大任务数: {MAX_TASKS}, 最大日志行数: {MAX_LOG_LINES}")

# 启动时验证配置
validate_config()

# ============ 状态映射配置 ============
STATUS_MAPPING = {
    "1": "Submitted - 已提交",
    "2": "Editor Assigned - 编辑已分配",
    "3": "Under Review - 正在审稿中",
    "4": "Reviews Completed (RRC) - 审稿已完成",
    "5": "Decision in Progress - 编辑评估中",
    "6": "Decision in Process - 决定中",
    "7": "Revision Required - 需要修订",
    "8": "With Editor - 编辑评估中",
    "9": "Accepted - 已接受",
    "39": "Rejected - 已拒稿",
    "29": "Decision in Progress - 编辑评估中",
    "sub": "Submitted - 已提交",
    "ur": "Under Review - 正在审稿中",
    "rrc": "Reviews Received - 等待决定",
    "dip": "Decision in Progress - 编辑评估中",
    "rev": "Revision Required - 需要修订",
    "acc": "Accepted - 已接受",
    "rej": "Rejected - 已拒稿"
}





# ============ 工具函数 ============
def display_time(ts):
    """将时间戳转换为北京时间显示"""
    if not ts:
        return None
    try:
        # 直接转换为北京时间，不再额外加5小时
        return datetime.fromtimestamp(ts, tz=timezone.utc).astimezone(CST)
    except (ValueError, OSError) as e:
        logger.error(f"时间转换错误: {e}")
        return datetime.now(CST)


def days_between(ts1, ts2=None):
    """计算两个时间戳之间的天数和秒数差"""
    try:
        dt1 = datetime.fromtimestamp(ts1, tz=timezone.utc)
        dt2 = datetime.now(timezone.utc) if ts2 is None else datetime.fromtimestamp(ts2, tz=timezone.utc)
        delta = dt2 - dt1
        return delta.days, delta.seconds
    except (ValueError, OSError) as e:
        logger.error(f"时间计算错误: {e}")
        return 0, 0


def status_description(code, reviewer_map=None):
    """获取状态描述，支持RRC状态检测"""
    code_str = str(code).lower().strip()

    # 特殊处理审稿中状态，检查是否为RRC
    if code_str == "3" and reviewer_map:
        revisions = [rev for (_, rev) in reviewer_map]
        max_rev = max(revisions) if revisions else None
        if max_rev is not None:
            accepted = completed = 0
            for (_, rev), events in reviewer_map.items():
                if rev != max_rev:
                    continue
                if any(ev["Event"] == "REVIEWER_ACCEPTED" for ev in events):
                    accepted += 1
                if any(ev["Event"] == "REVIEWER_COMPLETED" for ev in events):
                    completed += 1
            if accepted > 0 and accepted == completed:
                return STATUS_MAPPING["rrc"]

    return STATUS_MAPPING.get(code_str, f"未知状态（{code}）")


def status_text(code):
    """简单的状态文本获取"""
    return STATUS_MAPPING.get(str(code).lower(), f"未知状态（{code}）")


def extract_uuid_from_input(raw_input):
    """从输入中提取UUID，支持链接和纯UUID"""
    raw_input = raw_input.strip()
    if "uuid=" in raw_input:
        try:
            parsed = urllib.parse.urlparse(raw_input)
            params = urllib.parse.parse_qs(parsed.query)
            return params.get("uuid", [""])[0]
        except Exception as e:
            logger.error(f"UUID提取失败: {e}")
            return ""
    return raw_input


def validate_email_format(email):
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def mask_email(email):
    """邮箱脱敏处理"""
    if len(email) < 6:
        return email
    return email[:3] + '***' + email[-3:]


def validate_monitoring_config(sender_email, password, recipient_email, interval):
    """验证监控配置参数"""
    errors = []

    # 验证发件人邮箱
    if not sender_email.endswith('@qq.com'):
        errors.append("❌ 发件人邮箱：仅支持QQ邮箱（@qq.com）作为发件人")

    # 验证授权码
    if len(password.strip()) < 10:
        errors.append("❌ 授权码格式错误：QQ邮箱授权码应为16位字符，不是QQ密码")

    # 验证收件人邮箱
    if not validate_email_format(recipient_email):
        errors.append("❌ 收件人邮箱格式不正确")

    # 验证监控间隔
    if interval < 5:
        errors.append("❌ 监控间隔太短：为避免频繁请求，建议间隔至少5分钟")
    elif interval > 1440:  # 24小时
        errors.append("❌ 监控间隔太长：建议间隔不超过24小时（1440分钟）")

    return errors


def generate_hash(obj_list):
    filtered = [
        {"Id": ev["Id"], "Revision": ev["Revision"], "Event": ev["Event"], "Date": ev["Date"]}
        for ev in obj_list
    ]
    return hashlib.md5(json.dumps(filtered, sort_keys=True).encode()).hexdigest()


def write_log(recipient_email, uuid, message, threshold=None):
    """写入日志文件，支持重要日志保留"""
    threshold = threshold if threshold is not None else MAX_LOG_LINES
    masked = mask_email(recipient_email)
    filename = f"{masked}_{uuid}_log.txt"
    now = datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')
    new_entry = f"[{now}] ({masked}) {message}\n"

    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        else:
            lines = []

        lines.append(new_entry)

        # 判断是否为关键日志
        def is_important(entry):
            return any(key in entry for key in ['📢 通知发送', '📧 邮件已发送'])

        # 分离重要和非重要日志
        important_logs = [line for line in lines if is_important(line)]
        other_logs = [line for line in lines if not is_important(line)]

        # 限制非重要日志保留条数
        if len(other_logs) > threshold:
            other_logs = other_logs[-threshold:]

        # 合并保存
        new_lines = other_logs + important_logs
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)

    except Exception as e:
        print(f"日志写入错误: {e}")


def send_email(subject, body, sender_email, password, recipient_email, uuid=None):
    msg = MIMEText(body.replace('\n', '<br>'), 'html')
    msg['Subject'] = subject
    msg['From'] = sender_email
    msg['To'] = recipient_email
    try:
        if not sender_email.endswith('@qq.com'):
            raise ValueError("仅支持 QQ 邮箱作为发件人")
        
        # 更详细的授权码验证
        if len(password) < 10:
            raise ValueError("QQ邮箱授权码长度应为16位字符")
        
        server = smtplib.SMTP('smtp.qq.com', 587)
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, [recipient_email], msg.as_string())
        server.quit()
        if uuid:
            write_log(recipient_email, uuid, f"📧 邮件已发送: {subject}", threshold=MAX_LOG_LINES)
        return True, "发送成功"
    except smtplib.SMTPAuthenticationError as e:
        error_msg = "❌ 邮箱认证失败：请检查QQ邮箱地址和授权码是否正确。授权码不是QQ密码，需要在QQ邮箱设置中生成。"
        if uuid:
            write_log(recipient_email, uuid, f"❌ 邮件发送失败: {error_msg}", threshold=MAX_LOG_LINES)
        return False, error_msg
    except smtplib.SMTPConnectError as e:
        error_msg = "❌ 无法连接到QQ邮箱服务器，请检查网络连接。"
        if uuid:
            write_log(recipient_email, uuid, f"❌ 邮件发送失败: {error_msg}", threshold=MAX_LOG_LINES)
        return False, error_msg
    except smtplib.SMTPRecipientsRefused as e:
        error_msg = "❌ 收件人邮箱地址被拒绝，请检查收件人邮箱格式是否正确。"
        if uuid:
            write_log(recipient_email, uuid, f"❌ 邮件发送失败: {error_msg}", threshold=MAX_LOG_LINES)
        return False, error_msg
    except Exception as e:
        error_str = str(e)
        if "535" in error_str:
            error_msg = "❌ QQ邮箱授权码错误：请确保已开启SMTP服务并使用正确的16位授权码。"
        elif "550" in error_str:
            error_msg = "❌ 邮箱地址格式错误或不存在，请检查发件人和收件人邮箱地址。"
        elif "timeout" in error_str.lower():
            error_msg = "❌ 连接超时，请检查网络连接或稍后重试。"
        else:
            error_msg = f"❌ 邮件发送失败: {error_str}"
        
        if uuid:
            write_log(recipient_email, uuid, f"❌ 邮件发送失败: {error_msg}", threshold=MAX_LOG_LINES)
        return False, error_msg


# 接下来继续补全完整的监控任务功能和 UUID 查询功能（合并）
# （前略）前面部分略去，这里继续追加 Flask 表单、任务检查、监控视图、UUID 查询视图等功能

# ============ Flask 表单定义 ============
class MonitorForm(FlaskForm):
    sender_email = StringField('发件人邮箱(仅支持QQ邮箱)', validators=[DataRequired(), Email()])
    password = PasswordField('邮箱授权码', validators=[DataRequired()])
    recipient_email = StringField('收件人邮箱(支持所有邮箱)', validators=[DataRequired(), Email()])
    uuid = StringField('稿件UUID或Track链接', validators=[DataRequired()])
    interval = IntegerField('检查间隔（分钟）', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('开始监控')


class LoginForm(FlaskForm):
    username = StringField('管理员账号', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])
    submit = SubmitField('登录')


class ConfigForm(FlaskForm):
    log_lines = IntegerField('每个日志最多保留条数', validators=[DataRequired(), NumberRange(min=10, max=1000)])
    max_tasks = IntegerField('最大监控任务数', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('更新设置')


# ============ 检查任务函数 ============
def check_status(task_id):
    """检查单个监控任务的状态"""
    task = monitor_tasks.get(task_id)
    if not task:
        logger.warning(f"任务 {task_id} 不存在")
        return

    try:
        url = f"{API_BASE_URL}/{task['uuid']}"
        response = requests.get(url, timeout=10)
        response.raise_for_status()  # 抛出HTTP错误

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("响应格式不正确或不包含 Status 字段")

        cur_status = data.get("Status")
        cur_updated = data.get("LastUpdated")
        reviewers_data = data.get("ReviewEvents", [])
        current_review_hash = generate_hash(reviewers_data)

        reviewer_map = {}
        for ev in reviewers_data:
            reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)

        status_str = status_description(cur_status, reviewer_map)
        write_log(task['recipient_email'], task['uuid'],
                  f"🔍 定时检查，状态: {status_str}，更新时间: {display_time(cur_updated).strftime('%Y-%m-%d %H:%M:%S')}", threshold=MAX_LOG_LINES)

        should_notify = False
        reasons = []
        if task['last_status'] != cur_status:
            should_notify = True
            reasons.append("状态码变化")
        if task['last_updated'] != cur_updated:
            should_notify = True
            reasons.append("更新时间变化")
        if task.get("review_hash") != current_review_hash:
            should_notify = True
            reasons.append("审稿人信息变化")

        revision_max = max([r.get("Revision", 0) for r in reviewers_data], default=0)
        current_round = [r for r in reviewers_data if r.get("Revision", 0) == revision_max]
        reviewer_ids = set(ev["Id"] for ev in current_round if ev["Event"] == "Reviewer Invited")
        completed_ids = set(
            ev["Id"] for ev in current_round if ev["Event"] in {"Review Completed", "Declined", "Unassigned"})
        is_rrc = reviewer_ids and reviewer_ids.issubset(completed_ids)
        if is_rrc:
            should_notify = True
            reasons.append("本轮所有审稿人已完成（RRC）")

        if should_notify:
            reviewer_map = {}
            for ev in reviewers_data:
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)

            status_str = status_description(cur_status, reviewer_map)
            subject = "📢 稿件状态更新通知"
            body = (
                f"📄 <b>稿件标题：</b>{data.get('ManuscriptTitle')}\n"
                f"🔢 <b>稿件编号：</b>{data.get('PubdNumber')}\n"
                f"📚 <b>投稿期刊：</b>{data.get('JournalName')}\n"
                f"🧑‍🔬 <b>第一作者：</b>{data.get('FirstAuthor')}\n"
                f"📧 <b>通讯作者：</b>{data.get('CorrespondingAuthor')}\n\n"
                f"📌 <b>当前状态：</b>{status_str}\n"
                f"🕓 <b>最近更新时间：</b>{display_time(cur_updated).strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🔍 <b>变动原因：</b>{'、'.join(reasons)}\n\n"
                f"⏰ <b>检查时间：</b>{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')} (北京时间)\n"
                f"📡 本系统将持续每 {task['interval']} 分钟自动检测一次，如有变化将再次通知。"
            )

            send_email_result, send_error = send_email(subject, body, task['sender_email'], task['password'], task['recipient_email'], task['uuid'])
            if send_email_result:
                write_log(task['recipient_email'], task['uuid'], f"📢 通知发送成功，原因: {'、'.join(reasons)}",
                          threshold=MAX_LOG_LINES)
            else:
                write_log(task['recipient_email'], task['uuid'], f"📢 通知发送失败: {send_error}",
                          threshold=MAX_LOG_LINES)
            task['last_status'] = cur_status
            task['last_updated'] = cur_updated
            task['review_hash'] = current_review_hash
        task['fail_count'] = 0

    except Exception as e:
        # 初始化或更新失败计数器
        task.setdefault('fail_count', 0)
        task['fail_count'] += 1

        if task['fail_count'] >= 3:
            write_log(task['recipient_email'], task['uuid'], f"❌ 连续失败 {task['fail_count']} 次，任务终止: {e}",
                      threshold=MAX_LOG_LINES)
            scheduler.remove_job(task_id)
            recipient = task['recipient_email']
            del monitor_tasks[task_id]

            # 删除日志文件
            filename = f"{recipient}_log.txt"
            if os.path.exists(filename):
                os.remove(filename)
        else:
            write_log(task['recipient_email'], task['uuid'], f"⚠️ 第 {task['fail_count']} 次检查失败（未终止）: {e}",
                      threshold=MAX_LOG_LINES)


# 接下来将继续追加主页、监控任务提交、查询页面等 Flask 路由和 HTML 模板字符串

# （前略）前面定义的工具函数和定时任务略去

# ============ 监控任务主界面 ============
@app.route('/', methods=['GET', 'POST'])
@app.route('/', methods=['GET', 'POST'])
def index():
    global MAX_TASKS
    form = MonitorForm()
    can_submit = len(monitor_tasks) < MAX_TASKS
    if form.validate_on_submit() and can_submit:
        # 1. 验证配置参数
        config_errors = validate_monitoring_config(
            form.sender_email.data,
            form.password.data,
            form.recipient_email.data,
            form.interval.data
        )

        if config_errors:
            return render_template_string(TEMPLATE, form=form, error=config_errors[0],
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)

        task_id = f"task_{len(monitor_tasks) + 1}"
        raw_uuid = form.uuid.data.strip()

        # 4. UUID格式初步验证
        if len(raw_uuid) < 10:
            return render_template_string(TEMPLATE, form=form, error="❌ UUID格式错误：请输入完整的UUID或Track链接。",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)

        # 5. 提取UUID
        uuid = extract_uuid_from_input(raw_uuid)

        # 6. UUID格式进一步验证
        if not uuid or len(uuid.strip()) < 10:
            return render_template_string(TEMPLATE, form=form, error="❌ UUID提取失败：请检查输入的链接或UUID格式是否正确。",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)
        
        # 6. 检查重复监控
        for task in monitor_tasks.values():
            if task['recipient_email'] == form.recipient_email.data and task['uuid'] == uuid:
                flash("⚠️ 重复监控：当前收件人已经在监控该稿件，若要重新提交请联系管理员！", "warning")
                return redirect(url_for('index'))
        if task_id in monitor_tasks:
            flash(f"⚠️ 任务冲突：该稿件已在监控中，若要重新提交请联系管理员！", "warning")
            return redirect(url_for('index'))
            
        task = {
            'sender_email': form.sender_email.data.strip(),
            'password': form.password.data.strip(),
            'recipient_email': form.recipient_email.data.strip(),
            'uuid': uuid.strip(),
            'interval': form.interval.data,
            'last_status': None,
            'last_updated': None,
            'review_hash': None,
            'fail_count': 0
        }
        try:
            # 7. 测试UUID有效性
            url = f"{API_BASE_URL}/{task['uuid']}"
            response = requests.get(url, timeout=10)
            if response.status_code != 200:
                raise ValueError(f"API响应错误（状态码：{response.status_code}）")

            try:
                data = response.json()
            except ValueError:
                raise ValueError("UUID无效：服务器返回的不是有效的JSON数据，请检查UUID是否正确。")

            if not isinstance(data, dict) or "Status" not in data:
                raise ValueError("UUID无效：该UUID对应的稿件不存在或无法访问。")
                
            task['last_status'] = data.get("Status")
            task['last_updated'] = data.get("LastUpdated")
            task["review_hash"] = generate_hash(data.get("ReviewEvents", []))

            subject = "📡 稿件监控已启动"
            reviewer_map = {}
            for ev in data.get("ReviewEvents", []):
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)
            status_str = status_description(data.get("Status"), reviewer_map)
            body = f"""
                        <b>📡 您的稿件监控任务已成功启动！</b><br><br>
                        📄 <b>稿件标题：</b>{data.get('ManuscriptTitle')}<br>
                        🔢 <b>稿件编号：</b>{data.get('PubdNumber')}<br>
                        📚 <b>投稿期刊：</b>{data.get('JournalName')}<br>
                        🧑‍🔬 <b>第一作者：</b>{data.get('FirstAuthor')}<br>
                        📧 <b>通讯作者：</b>{data.get('CorrespondingAuthor')}<br><br>
                        📌 <b>当前状态：</b>{status_str}<br>
                        🕓 <b>最近更新时间：</b>{display_time(data.get('LastUpdated')).strftime('%Y-%m-%d %H:%M:%S')}<br>
                        ⏰ <b>监控频率：</b>每 {task['interval']} 分钟自动检测一次<br><br>

                        ✅ 系统将在检测到以下任意变动时发送通知：<br>
                        - 状态码变化<br>
                        - 更新时间变化<br>
                        - 最新一轮审稿人全部完成（RRC）<br><br>

                        📅 启动时间：{datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')}（北京时间）<br>
                        🔒 如需关闭，请联系管理员或在平台页面操作。
                        """
            # 8. 测试邮件发送
            success, error_msg = send_email(subject, body, task['sender_email'], task['password'], task['recipient_email'], task['uuid'])
            if not success:
                return render_template_string(TEMPLATE, form=form, error=error_msg,
                                              monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                              max_tasks=MAX_TASKS, can_submit=can_submit)
                                              
            reviewer_map = {}
            for ev in data.get("ReviewEvents", []):
                reviewer_map.setdefault((ev["Id"], ev["Revision"]), []).append(ev)
            status_str = status_description(data.get("Status"), reviewer_map)
            write_log(task['recipient_email'], task['uuid'],
                      f"📡 监控已启动，状态: {status_description(data.get('Status'), reviewer_map)}",
                      threshold=MAX_LOG_LINES)
        except requests.exceptions.Timeout:
            return render_template_string(TEMPLATE, form=form, error="❌ 网络超时：连接Elsevier服务器超时，请检查网络连接或稍后重试。",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)
        except requests.exceptions.ConnectionError:
            return render_template_string(TEMPLATE, form=form, error="❌ 网络连接失败：无法连接到Elsevier服务器，请检查网络连接。",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)
        except Exception as e:
            error_str = str(e)
            if "UUID" in error_str or "uuid" in error_str:
                error_msg = f"❌ UUID验证失败：{error_str}"
            elif "timeout" in error_str.lower():
                error_msg = "❌ 请求超时：连接Elsevier服务器超时，请稍后重试。"
            else:
                error_msg = f"❌ 启动失败：{error_str}"
                
            write_log(task['recipient_email'], task['uuid'], f"❌ 启动失败: {error_msg}", threshold=MAX_LOG_LINES)
            return render_template_string(TEMPLATE, form=form, error=error_msg,
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)

        monitor_tasks[task_id] = task
        scheduler.add_job(id=task_id, func=check_status, args=[task_id],
                          trigger='interval', minutes=task['interval'], max_instances=3, coalesce=True)
        flash("✅ 监控任务启动成功！已发送确认邮件，请查收。", "success")
        return redirect(url_for('index'))

    return render_template_string(TEMPLATE, form=form, monitor_tasks=monitor_tasks,
                                  admin=session.get("admin", False), max_tasks=MAX_TASKS,
                                  can_submit=can_submit, error=None)


# 删除监控任务
@app.route('/delete/<task_id>', methods=['POST'])
def delete_task(task_id):
    if session.get("admin") and task_id in monitor_tasks:
        task = monitor_tasks[task_id]
        recipient_email = task['recipient_email']
        masked = mask_email(recipient_email)
        log_filename = f"{masked}_{task['uuid']}_log.txt"

        # 停止定时任务
        scheduler.remove_job(task_id)

        # 写入删除日志（需加 uuid）
        write_log(recipient_email, task['uuid'], f"🗑️ 管理员删除任务 {task_id}，日志文件即将删除",
                  threshold=MAX_LOG_LINES)

        # 删除日志文件（如果存在）
        if os.path.exists(log_filename):
            os.remove(log_filename)

        # 删除监控任务
        del monitor_tasks[task_id]

    return redirect(url_for('index'))


# 管理员登录
@app.route('/admin', methods=['GET', 'POST'])
def admin():
    form = LoginForm()
    if form.validate_on_submit():
        if form.username.data == ADMIN_USERNAME and form.password.data == ADMIN_PASSWORD:
            session['admin'] = True
            return redirect(url_for('admin_config'))
    return render_template_string(ADMIN_TEMPLATE, form=form)


@app.route('/admin/config', methods=['GET', 'POST'])
def admin_config():
    global MAX_TASKS, MAX_LOG_LINES
    if not session.get("admin"):
        return redirect(url_for('admin'))
    form = ConfigForm()
    if form.validate_on_submit():
        MAX_TASKS = form.max_tasks.data
        MAX_LOG_LINES = form.log_lines.data
        flash("✅ 设置已更新", "success")
        return redirect(url_for('index'))  # 更新后返回主页面
    form.max_tasks.data = MAX_TASKS
    form.log_lines.data = MAX_LOG_LINES
    return render_template_string(CONFIG_TEMPLATE, form=form, current=MAX_TASKS, current_log_lines=MAX_LOG_LINES)


@app.route('/logout')
def logout():
    session.pop("admin", None)
    flash("您已成功退出管理员模式。", "info")
    return redirect(url_for('index'))


@app.route('/logs/<masked_email>/<uuid>')
def view_log(masked_email, uuid):
    if not session.get("admin"):
        return redirect(url_for('admin'))

    # 从 monitor_tasks 中查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        masked = task['recipient_email'][:3] + '***' + task['recipient_email'][-3:]
        if masked == masked_email:
            real_email = task['recipient_email']
            break

    if not real_email:
        return f"⚠️ 无法找到对应邮箱: {masked_email}"

    masked = real_email[:3] + '***' + real_email[-3:]
    filename = f"{masked}_{uuid}_log.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        content = ''.join(reversed(lines))  # 最新在上方显示
    except FileNotFoundError:
        content = "⚠️ 无日志文件或尚未生成。"

    return render_template_string(LOG_TEMPLATE, masked_email=masked_email, content=content, uuid=uuid)


@app.route('/clear_log/<masked_email>/<uuid>', methods=['POST'])
def clear_log(masked_email, uuid):
    if not session.get("admin"):
        return redirect(url_for('admin'))

    # 查找真实邮箱
    real_email = None
    for task in monitor_tasks.values():
        masked = task['recipient_email'][:3] + '***' + task['recipient_email'][-3:]
        if masked == masked_email:
            real_email = task['recipient_email']
            break

    if not real_email:
        flash("⚠️ 无法识别的邮箱标识", "warning")
        return redirect(url_for('index'))

    masked = real_email[:3] + '***' + real_email[-3:]
    filename = f"{masked}_{uuid}_log.txt"

    try:
        if os.path.exists(filename):
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("")
        flash("✅ 日志已清空", "success")
    except Exception as e:
        flash(f"❌ 日志清空失败: {e}", "danger")

    return redirect(url_for('view_log', masked_email=masked_email, uuid=uuid))


# 下一步将追加 query 查询视图与其模板字符串

# （前略）

# ============ UUID 查询入口与结果展示 ============
@app.route('/query', methods=['GET', 'POST'])
def query():
    """UUID查询入口"""
    if request.method == "POST":
        raw_input = request.form.get("uuid", "").strip()
        uuid = extract_uuid_from_input(raw_input)
        if uuid:
            return redirect(f"/result?uuid={uuid}")
    return render_template_string(QUERY_INPUT_TEMPLATE)


@app.route('/result')
def result():
    """显示查询结果"""
    raw_uuid = request.args.get("uuid", "").strip()
    uuid = extract_uuid_from_input(raw_uuid)
    api_url = f"{API_BASE_URL}/{uuid}"
    try:
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()  # 抛出HTTP错误

        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("数据格式错误")

        reviewers = []
        reviewer_map = {}
        for ev in data.get("ReviewEvents", []):
            rid = ev["Id"]
            rev = ev["Revision"]
            reviewer_map.setdefault((rid, rev), []).append(ev)

        invited_count = len(set(k for k in reviewer_map))
        accepted_count = 0
        
        # 使用 display_time 函数计算提交时间和最近更新时间
        submission_date = display_time(data.get("SubmissionDate"))
        last_updated = display_time(data.get("LastUpdated"))
        status_code = str(data.get("Status")).strip()
        if status_code in ["9", "39"]: 
            elapsed = last_updated - submission_date
            elapsed_days = elapsed.days
            elapsed_hours = elapsed.seconds // 3600
            elapsed_minutes = (elapsed.seconds % 3600) // 60
            elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（已完结）"
        else:
            current_time = datetime.now(timezone.utc)
            elapsed = current_time - submission_date
            elapsed_days = elapsed.days
            elapsed_hours = elapsed.seconds // 3600
            elapsed_minutes = (elapsed.seconds % 3600) // 60
            elapsed_str = f"{elapsed_days} 天 {elapsed_hours} 小时 {elapsed_minutes} 分钟（进行中）"
        
        for (rid, rev), actions in reviewer_map.items():
            invite_time = accept_time = complete_time = None
            for a in actions:
                if a["Event"] == "REVIEWER_INVITED":
                    invite_time = a["Date"]
                elif a["Event"] == "REVIEWER_ACCEPTED":
                    accept_time = a["Date"]
                elif a["Event"] == "REVIEWER_COMPLETED":
                    complete_time = a["Date"]

            if not accept_time:
                continue
            accepted_count += 1

            # 接受邀请耗时
            dd, ss = days_between(invite_time, accept_time) if invite_time else (0, 0)

            # 展示时间（+5小时）
            accept_display = display_time(accept_time)
            complete_display = display_time(complete_time) if complete_time else None

            # 审稿耗时（完成 - 接受）
            if complete_display:
                delta = complete_display - accept_display
                ed = delta.days
                es = delta.seconds
                review_time_str = f"{ed}天 {es // 3600}小时 {es % 3600 // 60}分 {es % 60}秒"
            else:
                review_time_str = "尚未完成"

            # 当前耗时（现在 - 接受展示时间）
            now = datetime.now(CST)
            elapsed = now - accept_display
            elapsed_d = elapsed.days
            elapsed_s = elapsed.seconds
            review_elapsed_str = None if complete_time else f"{elapsed_d}天 {elapsed_s // 3600}小时 {elapsed_s % 3600 // 60}分 {elapsed_s % 60}秒"


            reviewers.append({
                "id": rid,
                "revision": rev,
                "invite_time": display_time(invite_time).strftime('%Y-%m-%d %H:%M:%S') if invite_time else "—",
                "accept_time": accept_display.strftime('%Y-%m-%d %H:%M:%S'),
                "complete_time": complete_display.strftime('%Y-%m-%d %H:%M:%S') if complete_display else "未完成",
                "duration": f"{dd}天 {ss // 3600}小时 {ss % 3600 // 60}分 {ss % 60}秒",
                "review_time": review_time_str,
                "review_elapsed": review_elapsed_str
            })

        group_stats = {}
        for (rid, rev), actions in reviewer_map.items():
            invited = any(a["Event"] == "REVIEWER_INVITED" for a in actions)
            accepted = any(a["Event"] == "REVIEWER_ACCEPTED" for a in actions)
            completed = any(a["Event"] == "REVIEWER_COMPLETED" for a in actions)
            group_stats.setdefault(rev, {"invited": 0, "accepted": 0, "completed": 0})
            if invited:
                group_stats[rev]["invited"] += 1
            if accepted:
                group_stats[rev]["accepted"] += 1
            if completed:
                group_stats[rev]["completed"] += 1

        status_str = status_description(data.get("Status"), reviewer_map)
        
        # 计算当前审稿轮次（最新的轮次）
        current_round = max(group_stats.keys()) + 1 if group_stats else 1

        return render_template_string(QUERY_RESULT_TEMPLATE,
                                      title=data.get("ManuscriptTitle"),
                                      pubd_number=data.get("PubdNumber"),
                                      journal=data.get("JournalName"),
                                      first_author=data.get("FirstAuthor"),
                                      corresponding_author=data.get("CorrespondingAuthor"),
                                      uuid=uuid,
                                      status=status_str,
                                      elapsed=elapsed_str,
                                      updated=display_time(data.get("LastUpdated")).strftime('%Y-%m-%d %H:%M:%S'),
                                      submitted=display_time(data.get("SubmissionDate")).strftime('%Y-%m-%d %H:%M:%S'),
                                      invited_count=invited_count,
                                      accepted_count=accepted_count,
                                      reviewers=reviewers,
                                      group_stats=group_stats,
                                      current_round=current_round,
                                      query_time=datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S'))
    except Exception:
        return render_template_string("""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <title>查询失败</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body {
                    background-color: #f8f9fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .card {
                    max-width: 500px;
                    width: 100%;
                    padding: 2rem;
                    border-radius: 12px;
                    box-shadow: 0 0 12px rgba(0, 0, 0, 0.08);
                    background-color: white;
                }
                .highlight {
                    color: #d63384;
                    font-weight: bold;
                }
            </style>
        </head>
            <body>
                <div class="card text-center">
                    <h4 class="mb-4 text-danger">❌ 查询失败</h4>
                    <p class="text-muted">
                        您输入的内容不是有效的 UUID 或 Track 链接。<br>
                        请确保输入的是 <strong>稿件状态链接</strong> 或其末尾的 <strong>UUID</strong>，而非稿件编号或投稿编号。<br>
                        正确格式示例：<br>
                        <code><span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code>
                        或<br>
                        <code>https://.../tracker?uuid=<span class="highlight">81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code><br><br>
                        📬 提醒：该链接通常在稿件<strong>第一次送审后</strong>由系统发送至<strong>通讯作者邮箱</strong>，请注意查收相关邮件。
                    </p>
                    <div class="d-grid gap-2 mt-4">
                        <a href="/query" class="btn btn-outline-primary btn-lg">🔙 返回重新输入</a>
                        <a href="/" class="btn btn-outline-secondary btn-lg">📡 返回监控主页</a>
                    </div>
                    <div class="text-center text-muted mt-5 mb-3">
                            © 2025 小红书博主 <strong>ZeroFive</strong> 版权所有
                    </div>
                </div>
            </body>
        </html>
        """)


# 接下来将追加 QUERY_INPUT_TEMPLATE, QUERY_RESULT_TEMPLATE, TEMPLATE 等 HTML 模板

# （前略）前面为主程序、工具函数与所有视图函数定义

# ============ 所有 HTML 模板字符串 ============

TEMPLATE = '''
<!DOCTYPE html>
<html><head>
    <title>稿件监控平台</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 900px;
        }
        .form-control, .btn { 
            font-size: 1rem;
            border-radius: 10px;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
            font-size: 2.2rem;
        }
        .task-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            position: relative;
        }



        .nav-buttons {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        
        /* 增强页面整体视觉效果 */
        .alert {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        .alert-info {
            background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
            color: #0c5460;
        }
        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }
        
        /* 启动监控加载效果 */
        .btn-loading {
            position: relative;
            color: transparent !important;
            pointer-events: none;
        }
        
        .btn-loading::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: button-loading-spinner 1s ease infinite;
        }
        
        @keyframes button-loading-spinner {
            from {
                transform: rotate(0turn);
            }
            to {
                transform: rotate(1turn);
            }
        }
        
        .btn-loading::before {
            content: "🚀 正在启动监控...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: 600;
            white-space: nowrap;
            animation: pulse-text 1.5s ease-in-out infinite;
        }
        
        @keyframes pulse-text {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }
        
        /* 启动成功的动画效果 */
        .submit-success {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4) !important;
        }
        
        .submit-success::before {
            content: "✅ 启动成功！";
            color: white;
            animation: none;
        }
        
        .submit-success::after {
            display: none;
        }
    </style>
</head><body>
<div class="container my-4">
    <div class="main-container p-4">
        <h2 class="page-title mb-4 text-center">
            <i class="fas fa-file-alt me-2"></i>Elsevier 稿件监控平台
        </h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-lg-6">
                <form method="post">{{ form.hidden_tag() }}
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-envelope me-2"></i>{{ form.sender_email.label.text }}</label>
                        {{ form.sender_email(class="form-control", placeholder="例：<EMAIL>") }}
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>仅支持QQ邮箱，需开启SMTP服务
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-key me-2"></i>{{ form.password.label.text }}</label>
                        {{ form.password(class="form-control", placeholder="16位授权码，非QQ密码") }}
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>在QQ邮箱设置→账户→开启SMTP服务获取16位授权码
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-inbox me-2"></i>{{ form.recipient_email.label.text }}</label>
                        {{ form.recipient_email(class="form-control", placeholder="例：<EMAIL>") }}
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>支持所有邮箱，用于接收稿件状态通知
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-link me-2"></i>{{ form.uuid.label.text }}</label>
                        {{ form.uuid(class="form-control", placeholder="UUID或完整Track链接") }}
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>稿件首次送审后Elsevier发送的Track链接或其中的UUID
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><i class="fas fa-clock me-2"></i>{{ form.interval.label.text }}</label>
                        {{ form.interval(class="form-control", placeholder="建议30-60分钟", value="30") }}
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>最短5分钟，最长24小时（1440分钟）
                        </small>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>温馨提示：</strong>
                            <ul class="mb-0 mt-2">
                                <li>QQ邮箱授权码获取：QQ邮箱→设置→账户→开启SMTP服务</li>
                                <li>Track链接来源：稿件首次送审后系统发送至通讯作者邮箱</li>
                                <li>监控频率：建议30-60分钟，避免过于频繁</li>
                            </ul>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success w-100" {% if not can_submit %}disabled{% endif %}>开始监控</button>
                </form>

                {% if error %}
                  <div class="alert alert-danger mt-3" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                  </div>
                {% endif %}
            </div>
            
            <div class="col-lg-6">
                <h4 class="mt-4 mt-lg-0 mb-3">
                    <i class="fas fa-tasks me-2"></i>当前监控任务（{{ monitor_tasks|length }}/{{ max_tasks }}）
                </h4>
                <div class="task-list" style="max-height: 400px; overflow-y: auto;">
                    {% if monitor_tasks %}
                    {% for task_id, task in monitor_tasks.items() %}
                    <div class="task-item p-3 mb-2">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="mb-2 mb-md-0">
                                <div><i class="fas fa-user me-2"></i><strong>收件人:</strong> {{ task.recipient_email[:3] + '***' + task.recipient_email[-3:] }}</div>
                                <div><i class="fas fa-stopwatch me-2"></i><strong>间隔:</strong> {{ task.interval }} 分钟</div>
                            </div>
                            {% if admin %}
                                <div class="d-flex gap-2">
                                    <button class="btn btn-danger btn-sm" onclick="if(confirm('确认删除该监控任务吗？')) { document.getElementById('deleteForm{{ loop.index }}').submit(); }">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <form id="deleteForm{{ loop.index }}" method="post" action="{{ url_for('delete_task', task_id=task_id) }}" style="display: none;"></form>
                                    <a class="btn btn-outline-info btn-sm" href="{{ url_for('view_log', masked_email=task.recipient_email[:3] + '***' + task.recipient_email[-3:], uuid=task.uuid) }}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                        <p>暂无监控任务</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>



        <!-- 自定义监控确认模态框 -->
        <div class="modal fade custom-confirm-modal" id="confirmMonitorModal" tabindex="-1" aria-labelledby="confirmMonitorModalLabel" aria-hidden="true" data-bs-backdrop="false" data-bs-keyboard="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content fade-in">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmMonitorModalLabel">
                            <i class="fas fa-satellite-dish me-2"></i>确认启动监控任务
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-circle warning-icon"></i>
                        </div>
                        <h6 class="text-center mb-4 text-primary">请仔细确认以下监控配置信息：</h6>
                        
                        <div class="info-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-envelope text-primary me-3"></i>
                                <div>
                                    <strong>发件邮箱：</strong>
                                    <span id="confirm-sender-email" class="text-dark"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-inbox text-primary me-3"></i>
                                <div>
                                    <strong>收件邮箱：</strong>
                                    <span id="confirm-recipient-email" class="text-dark"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock text-primary me-3"></i>
                                <div>
                                    <strong>监控间隔：</strong>
                                    <span id="confirm-interval" class="text-dark"></span> 分钟
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-fingerprint text-primary me-3"></i>
                                <div>
                                    <strong>稿件UUID：</strong>
                                    <small id="confirm-uuid" class="text-dark font-monospace"></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>重要提醒：</strong>
                            <ul class="mb-0 mt-2">
                                <li>启动后系统将立即发送测试邮件验证配置</li>
                                <li>请确保邮箱地址和授权码完全正确</li>
                                <li>监控任务将按设定间隔自动检测稿件状态</li>
                                <li>检测到状态变化时会自动发送通知邮件</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>常见问题：</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>授权码错误：</strong>确保使用16位QQ邮箱授权码，非QQ密码</li>
                                <li><strong>SMTP未开启：</strong>需在QQ邮箱设置中开启SMTP服务</li>
                                <li><strong>UUID无效：</strong>确保使用稿件首次送审后的Track链接中的UUID</li>
                                <li><strong>网络问题：</strong>确保能正常访问Elsevier和QQ邮箱服务器</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-cancel me-3" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>取消
                        </button>
                        <button type="button" class="btn btn-confirm" onclick="submitMonitorForm()">
                            <i class="fas fa-paper-plane me-2"></i>确认启动（将发送测试邮件）
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="nav-buttons text-center">
            <div class="d-flex flex-column flex-sm-row justify-content-center gap-3">
                {% if admin %}
                    <a href="{{ url_for('admin_config') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>设置
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>退出管理员
                    </a>
                {% else %}
                    <a href="{{ url_for('admin') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-lock me-2"></i>管理员登录
                    </a>
                {% endif %}
                <a href="{{ url_for('query') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>查询稿件详细状态
                </a>
            </div>
        </div>
        
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 简化的JavaScript，只保留必要功能
</script>
</body></html>
'''

ADMIN_TEMPLATE = '''
<!DOCTYPE html>
<html><head>
    <title>管理员登录</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
        }
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .lock-icon {
            font-size: 3rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    </style>
</head><body>
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
    <div class="login-container shadow" style="width: 100%; max-width: 450px;">
        <div class="text-center mb-4">
            <i class="fas fa-shield-alt lock-icon"></i>
            <h3 class="page-title mt-3">管理员登录</h3>
            <p class="text-muted">请输入管理员凭据</p>
        </div>
        <form method="post">{{ form.hidden_tag() }}
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-user text-primary"></i>
                    </span>
                    {{ form.username(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="管理员账号") }}
                </div>
            </div>
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-lock text-primary"></i>
                    </span>
                    {{ form.password(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="密码") }}
                </div>
            </div>
            <div class="d-grid">
                {{ form.submit(class="btn btn-primary btn-lg") }}
            </div>
        </form>
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
</body></html>
'''

CONFIG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>系统设置</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .config-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 550px;
            margin: 0 auto;
        }
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .settings-icon {
            font-size: 3rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: none;
        }
        .btn-nav {
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-nav:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
<div class="container">
    <div class="config-container shadow">
        <div class="text-center mb-4">
            <i class="fas fa-cogs settings-icon"></i>
            <h3 class="page-title mt-3">系统设置</h3>
            <p class="text-muted">管理监控系统的配置参数</p>
        </div>
        
        <div class="stat-card mb-4">
            <div class="row text-center">
                <div class="col-6">
                    <div class="mb-2">
                        <i class="fas fa-tasks text-primary me-2"></i>
                        <strong>当前最大任务数</strong>
                    </div>
                    <div class="h4 text-success mb-0">{{ current }}</div>
                </div>
                <div class="col-6">
                    <div class="mb-2">
                        <i class="fas fa-file-alt text-info me-2"></i>
                        <strong>日志保留条数</strong>
                    </div>
                    <div class="h4 text-success mb-0">{{ current_log_lines }}</div>
                </div>
            </div>
        </div>
        
        <form method="post">
            {{ form.hidden_tag() }}
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-hashtag text-primary"></i>
                    </span>
                    {{ form.max_tasks(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="最大任务数") }}
                </div>
                <small class="text-muted">设置系统可同时监控的最大任务数量</small>
            </div>
            
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light border-0" style="border-radius: 15px 0 0 15px;">
                        <i class="fas fa-list text-info"></i>
                    </span>
                    {{ form.log_lines(class="form-control border-start-0", style="border-radius: 0 15px 15px 0;", placeholder="日志保留条数") }}
                </div>
                <small class="text-muted">设置每个任务日志文件最多保留的条数</small>
            </div>
            
            <div class="d-grid mb-3">
                {{ form.submit(class="btn btn-success btn-lg") }}
            </div>
        </form>
        
        <div class="d-flex gap-2">
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-nav flex-fill">
                <i class="fas fa-arrow-left me-2"></i>返回主页
            </a>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-nav flex-fill">
                <i class="fas fa-sign-out-alt me-2"></i>退出管理员
            </a>
        </div>
        
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

LOG_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>日志查看 - {{ masked_email }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .log-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            padding: 2rem;
        }
        .log-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            padding: 1.5rem;
            font-size: 0.95rem;
            line-height: 1.6;
            overflow-wrap: break-word;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
            max-height: 60vh;
            overflow-y: auto;
        }
        .log-entry {
            padding: 1rem;
            border-left: 4px solid #dee2e6;
            margin-bottom: 0.8rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .log-entry:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .highlight {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745 !important;
        }
        .log-time {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 500;
        }
        .log-content {
            font-weight: 500;
            word-break: break-word;
            margin-top: 0.3rem;
        }
        .page-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }
        .btn-action {
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-action:hover {
            transform: translateY(-2px);
        }
        .header-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        @media (max-width: 576px) {
            .log-entry {
                padding: 0.75rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    <div class="container mb-3">
    {% for category, message in messages %}
      <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endfor %}
    </div>
  {% endif %}
{% endwith %}

<div class="container my-4">
    <div class="log-container">
        <div class="header-card text-center">
            <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
            <h3 class="page-title mb-2">� 日志查看</h3>
            <p class="text-muted mb-3">
                <i class="fas fa-user me-2"></i>用户: <strong>{{ masked_email }}</strong> 
                <span class="mx-2">|</span>
                <i class="fas fa-link me-2"></i>UUID: <strong>{{ uuid }}</strong>
            </p>
            
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-action">
                    <i class="fas fa-arrow-left me-2"></i>返回监控主页
                </a>
                <button class="btn btn-outline-danger btn-action" data-bs-toggle="modal" data-bs-target="#clearLogModal">
                    <i class="fas fa-trash me-2"></i>清空日志
                </button>
            </div>
        </div>
        
        <!-- 清空日志确认模态框 -->
        <div class="modal fade" id="clearLogModal" tabindex="-1" aria-labelledby="clearLogModalLabel" aria-hidden="true" data-bs-backdrop="false" data-bs-keyboard="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);">
                    <div class="modal-header" style="background: linear-gradient(45deg, #dc3545, #e83e8c); color: white; border-radius: 20px 20px 0 0;">
                        <h5 class="modal-title" id="clearLogModalLabel">
                            <i class="fas fa-exclamation-triangle me-2"></i>确认清空日志
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                        </div>
                        <h6 class="mb-3">您确定要清空这个日志文件吗？</h6>
                        <div class="alert alert-warning">
                            <strong>用户：</strong>{{ masked_email }}<br>
                            <strong>UUID：</strong>{{ uuid }}<br>
                            <small class="text-muted">此操作将永久删除所有日志记录且无法恢复</small>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-radius: 12px;">
                            <i class="fas fa-times me-2"></i>取消
                        </button>
                        <form method="post" action="{{ url_for('clear_log', masked_email=masked_email, uuid=uuid) }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger" style="background: linear-gradient(45deg, #dc3545, #e83e8c); border: none; border-radius: 12px;">
                                <i class="fas fa-trash me-2"></i>确认清空
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="log-box">
            {% for line in content.splitlines() %}
                {% set time = line.split("]")[0][1:] %}
                {% set message = line.split("]")[1].strip() %}
                {% set important = '📢' in message or '📧' in message %}
                <div class="log-entry {% if important %}highlight{% endif %}">
                    <div class="log-time">
                        <i class="fas fa-clock me-2"></i>{{ time }}
                    </div>
                    <div class="log-content">{{ message }}</div>
                </div>
            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-4x mb-3 opacity-50"></i>
                    <h5>暂无日志记录</h5>
                    <p>该任务还没有生成任何日志信息</p>
                </div>
            {% endfor %}
        </div>
        
        <div class="text-center text-muted mt-4">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# QUERY_INPUT_TEMPLATE 和 QUERY_RESULT_TEMPLATE 请在下一个步骤中继续追加
# （前略）

# ============ 查询 UUID 输入页模板 ============
QUERY_INPUT_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>稿件状态查询 - Elsevier监控平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .query-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 3rem;
            max-width: 600px;
            margin: 0 auto;
            animation: slideInUp 0.6s ease;
            position: relative;
            overflow: hidden;
        }

        .query-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .icon-large {
            font-size: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1.5rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .page-title {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .form-label {
            font-size: 1.1rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            font-size: 1.1rem;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            transform: translateY(-2px);
            background: white;
        }

        .form-control::placeholder {
            color: #adb5bd;
            font-style: italic;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2.5rem;
            font-size: 1.2rem;
            font-weight: 700;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 15px;
            padding: 0.8rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
            color: white;
        }
        .help-text {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2.5rem;
            border-left: 5px solid #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
        }

        .help-title {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
        }

        .help-text ul {
            margin-bottom: 0;
            color: #495057;
            font-size: 1rem;
            line-height: 1.6;
        }

        .help-text li {
            margin-bottom: 0.8rem;
            padding-left: 0.5rem;
        }

        .help-text strong {
            color: #667eea;
        }

        .copyright {
            color: #6c757d;
            font-size: 0.95rem;
            margin-top: 2rem;
            text-align: center;
        }

        /* 移动端响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .query-container {
                padding: 2rem 1.5rem;
                border-radius: 20px;
                margin: 10px;
            }

            .icon-large {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            .page-title {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .subtitle {
                font-size: 1rem;
                margin-bottom: 1.5rem;
            }

            .help-text {
                padding: 1.5rem;
                border-radius: 15px;
                margin-bottom: 2rem;
            }

            .help-title {
                font-size: 1.1rem;
                margin-bottom: 0.8rem;
            }

            .help-text ul {
                font-size: 0.95rem;
            }

            .form-control {
                font-size: 1rem;
                padding: 0.8rem 1.2rem;
                border-radius: 12px;
            }

            .btn-primary {
                font-size: 1.1rem;
                padding: 0.9rem 2rem;
                border-radius: 12px;
            }

            .btn-secondary {
                font-size: 1rem;
                padding: 0.7rem 1.5rem;
                border-radius: 12px;
            }

            .copyright {
                font-size: 0.9rem;
                margin-top: 1.5rem;
            }
        }


    </style>
    <script>
        window.onload = function() {
            const uuidInput = document.querySelector("input[name='uuid']");
            const lastUuid = localStorage.getItem("last_uuid");
            if (lastUuid && uuidInput) {
                uuidInput.value = lastUuid;
            }
            document.querySelector("form").addEventListener("submit", function() {
                localStorage.setItem("last_uuid", uuidInput.value);
            });
        }
    </script>
</head>
<body>
<div class="container">
    <div class="query-container shadow">
        <div class="text-center mb-4">
            <i class="fas fa-search icon-large"></i>
            <h1 class="page-title">稿件状态查询</h1>
            <p class="subtitle">快速查询您的Elsevier稿件审稿状态</p>
        </div>

        <div class="help-text">
            <div class="help-title">
                <i class="fas fa-info-circle me-2"></i>查询说明
            </div>
            <ul class="ps-3">
                <li><strong>UUID来源：</strong>稿件首次送审后，Elsevier系统发送至通讯作者邮箱的Track链接</li>
                <li><strong>支持格式：</strong>完整Track链接或其中的UUID字符串</li>
                <li><strong>示例格式：</strong>81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx</li>
                <li><strong>注意事项：</strong>请勿混淆稿件编号与UUID，两者为不同概念</li>
            </ul>
        </div>
        <form method="POST" autocomplete="off">
            <div class="mb-4">
                <label class="form-label" for="uuid-input">
                    <i class="fas fa-fingerprint me-2"></i>UUID 或 Track 链接
                </label>
                <input type="text"
                       id="uuid-input"
                       name="uuid"
                       class="form-control"
                       placeholder="请输入UUID或完整的Track链接，例：81891ed3-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                       required autofocus>
            </div>

            <div class="d-grid gap-3 mb-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>立即查询稿件状态
                </button>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回监控平台
                </a>
            </div>
        </form>

        <div class="copyright">
            © 2025 小红书博主 <strong>ZeroFive</strong> 版权所有
        </div>
    </div>
</div>
</body>
</html>
'''

# ============ 查询结果展示模板 ============
QUERY_RESULT_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>稿件状态详情</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 10px 0;
        }
        .result-container {
            background: #ffffff;
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            margin: 10px auto;
            padding: 2rem;
            max-width: 1200px;
            animation: slideInUp 0.6s ease;
        }
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .info-card {
            background: #ffffff;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        .badge-status { 
            font-size: 1rem;
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .badge-submitted { background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%); }
        .badge-assigned { background: linear-gradient(135deg, #6610f2 0%, #6f42c1 100%); }
        .badge-review { background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%); }
        .badge-rrc { background: linear-gradient(135deg, #20c997 0%, #0dcaf0 100%); }
        .badge-dip { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .badge-rev { background: linear-gradient(135deg, #6f42c1 0%, #d63384 100%); }
        .badge-acc { background: linear-gradient(135deg, #198754 0%, #20c997 100%); }
        .badge-rej { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
        .review-card { 
            border-left: 5px solid #667eea;
            background: #ffffff;
            border-radius: 15px;
            transition: all 0.4s ease;
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }
        .review-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            border-left-color: #764ba2;
        }
        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 800;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }
        .section-title i {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 0.5rem;
        }
        .page-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 800;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }
        .accordion-button {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px !important;
            font-weight: 700;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
        }
        .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .accordion-button:hover {
            transform: translateY(-2px);
        }
        .accordion-body {
            background: #ffffff;
            border-radius: 0 0 15px 15px;
            padding: 1.5rem;
        }
        .info-item {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            font-size: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-item .label {
            font-weight: 700;
            color: #495057;
            display: flex;
            align-items: center;
        }
        .info-item .label i {
            margin-right: 0.5rem;
            color: #667eea;
        }
        .info-item .value {
            color: #212529;
            font-weight: 500;
        }
        .btn-nav {
            border-radius: 20px;
            font-weight: 600;
            padding: 0.8rem 2rem;
            transition: all 0.4s ease;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .btn-nav:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            background: #ffffff;
            border-radius: 18px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }
        .stat-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: #212529;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 600;
        }
        .compact-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .compact-info .label {
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
        }
        .compact-info .value {
            font-size: 0.85rem;
            color: #6c757d;
            text-align: right;
            flex: 1;
            margin-left: 0.5rem;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                padding: 5px 0;
            }
            .result-container {
                margin: 5px;
                padding: 1.2rem;
                border-radius: 20px;
            }
            .page-title {
                font-size: 1.8rem;
            }
            .section-title {
                font-size: 1.3rem;
            }
            .info-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
                border-radius: 18px;
            }
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }
            .stat-item {
                padding: 1.2rem;
            }
            .stat-icon {
                font-size: 2rem;
            }
            .stat-number {
                font-size: 1.5rem;
            }
            .accordion-button {
                font-size: 0.95rem;
                padding: 1rem 1.2rem;
                border-radius: 12px !important;
            }
            .review-card {
                margin-bottom: 1.2rem;
                padding: 1.2rem;
            }
            .btn-nav {
                padding: 0.7rem 1.5rem;
                font-size: 0.95rem;
                margin-bottom: 0.5rem;
            }
            .info-item {
                padding: 0.7rem 0;
                font-size: 0.95rem;
                flex-direction: column;
                align-items: flex-start;
            }
            .info-item .value {
                margin-top: 0.3rem;
                font-size: 0.9rem;
            }
        }
        
        /* 超小屏幕优化 */
        @media (max-width: 576px) {
            .result-container {
                margin: 2px;
                padding: 1rem;
                border-radius: 15px;
            }
            .page-title {
                font-size: 1.5rem;
            }
            .section-title {
                font-size: 1.2rem;
            }
            .info-card {
                padding: 1.2rem;
                margin-bottom: 1.2rem;
            }
            .btn-nav {
                font-size: 0.9rem;
                padding: 0.6rem 1.2rem;
            }
            .compact-info {
                margin-bottom: 0.3rem;
            }
            .btn-nav {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
                margin-bottom: 0.5rem;
            }
            .info-item {
                font-size: 0.85rem;
                padding: 0.4rem 0;
            }
        }
        
        @media (max-width: 576px) {
            .compact-info {
                flex-direction: column;
                align-items: flex-start;
            }
            .compact-info .value {
                text-align: left;
                margin-left: 0;
                margin-top: 0.2rem;
            }
        }
    </style>
</head>
<body>
<div class="container-fluid my-2">
    <div class="result-container">
        <div class="text-center mb-3">
            <i class="fas fa-file-alt fa-3x mb-2" style="background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
            <h1 class="page-title">稿件状态详情</h1>
            <p class="text-muted small">实时查询结果 · Elsevier官方API</p>
        </div>

        <div class="info-card">
            <h4 class="section-title">
                <i class="fas fa-info-circle me-2"></i>基本信息
            </h4>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-heading me-1 text-primary"></i>标题</span>
                <span class="value">{{ title }}</span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-hashtag me-1 text-info"></i>编号</span>
                <span class="value">{{ pubd_number }}</span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-book me-1 text-success"></i>期刊</span>
                <span class="value">{{ journal }}</span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-user me-1 text-warning"></i>第一作者</span>
                <span class="value">{{ first_author }}</span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-envelope me-1 text-danger"></i>通讯作者</span>
                <span class="value">{{ corresponding_author }}</span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-flag me-1"></i>当前状态</span>
                <span class="value">
                    {% set badge_color = "badge-submitted" %}
                    {% if "已提交" in status %} {% set badge_color = "badge-submitted" %}
                    {% elif "已分配" in status %} {% set badge_color = "badge-assigned" %}
                    {% elif "审稿中" in status %} {% set badge_color = "badge-review" %}
                    {% elif "等待决定" in status %} {% set badge_color = "badge-rrc" %}
                    {% elif "评估中" in status %} {% set badge_color = "badge-dip" %}
                    {% elif "修订" in status %} {% set badge_color = "badge-rev" %}
                    {% elif "已接受" in status %} {% set badge_color = "badge-acc" %}
                    {% elif "已拒稿" in status %} {% set badge_color = "badge-rej" %}
                    {% endif %}
                    <span class="badge badge-status {{ badge_color }}">{{ status }}</span>
                </span>
            </div>
            <div class="info-item compact-info">
                <span class="label"><i class="fas fa-layer-group me-1 text-info"></i>当前轮次</span>
                <span class="value">
                    <span class="badge" style="background: linear-gradient(45deg, #17a2b8, #20c997); color: white; border-radius: 20px; padding: 0.3rem 0.6rem; font-size: 0.8rem;">
                        第{{ current_round }}轮
                    </span>
                </span>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <i class="fas fa-calendar-plus stat-icon"></i>
                <h6 class="mt-2 mb-1">提交时间</h6>
                <p class="mb-0 text-muted small">{{ submitted }}</p>
            </div>
            <div class="stat-item">
                <i class="fas fa-clock stat-icon"></i>
                <h6 class="mt-2 mb-1">稿件耗时</h6>
                <p class="mb-0 text-muted small">{{ elapsed }}</p>
            </div>
            <div class="stat-item">
                <i class="fas fa-sync-alt stat-icon"></i>
                <h6 class="mt-2 mb-1">最近更新</h6>
                <p class="mb-0 text-muted small">{{ updated }}</p>
            </div>
            <div class="stat-item">
                <i class="fas fa-users stat-icon"></i>
                <h6 class="mt-2 mb-1">审稿人统计</h6>
                <p class="mb-0 text-muted small">邀请{{ invited_count }}位，接受{{ accepted_count }}位</p>
            </div>
        </div>

        <h4 class="section-title">
            <i class="fas fa-user-tie me-2"></i>审稿人详情
        </h4>
        {% set grouped = {} %}
        {% for r in reviewers %}
            {% set _ = grouped.setdefault(r.revision, []).append(r) %}
        {% endfor %}
        <div class="accordion" id="reviewAccordion">
        {% for rev, group in grouped.items() %}
        <div class="accordion-item border-0 mb-2">
            <h2 class="accordion-header" id="heading{{ rev }}">
                <button class="accordion-button {% if rev != (grouped.keys()|list)[-1] %}collapsed{% endif %}" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#collapse{{ rev }}" 
                        aria-expanded="{% if rev == (grouped.keys()|list)[-1] %}true{% else %}false{% endif %}" 
                        aria-controls="collapse{{ rev }}">
                    <i class="fas fa-layer-group me-2"></i>
                    第{{ rev + 1 }}轮（邀请{{ group_stats[rev].invited }}，接受{{ group_stats[rev].accepted }}，完成{{ group_stats[rev].completed }}）
                </button>
            </h2>
            <div id="collapse{{ rev }}" class="accordion-collapse collapse {% if rev == (grouped.keys()|list)[-1] %}show{% endif %}" 
                 aria-labelledby="heading{{ rev }}" 
                 data-bs-parent="#reviewAccordion">
                <div class="accordion-body p-2">
                    {% for r in group %}
                        <div class="review-card p-3">
                            <h5 class="mb-2">
                                <i class="fas fa-user-circle me-2 text-primary"></i>
                                审稿人 {{ r.id }}
                            </h5>
                            <div class="compact-info">
                                <span class="label">📨 邀请时间</span>
                                <span class="value">{{ r.invite_time }}</span>
                            </div>
                            <div class="compact-info">
                                <span class="label">✅ 接受时间</span>
                                <span class="value">{{ r.accept_time }}</span>
                            </div>
                            <div class="compact-info">
                                <span class="label">📘 完成时间</span>
                                <span class="value">{{ r.complete_time }}</span>
                            </div>
                            <div class="compact-info">
                                <span class="label">⏱ 接受耗时</span>
                                <span class="value">{{ r.duration }}</span>
                            </div>
                            <div class="compact-info">
                                <span class="label">📗 审稿耗时</span>
                                <span class="value">{{ r.review_time }}</span>
                            </div>
                            {% if r.review_elapsed %}
                            <div class="compact-info">
                                <span class="label">📙 已审稿时长</span>
                                <span class="value">{{ r.review_elapsed }}</span>
                            </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endfor %}
        </div>

        <div class="text-center mt-3">
            <div class="d-flex justify-content-center gap-2 flex-wrap">
                <a href="/query" class="btn btn-primary btn-nav">
                    <i class="fas fa-redo me-1"></i>重新查询
                </a>
                <a href="/" class="btn btn-outline-secondary btn-nav">
                    <i class="fas fa-home me-1"></i>返回监控平台
                </a>
            </div>
            <div style="border-top:1px solid #f0f0f0; padding-top: 1rem;">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    实时数据 | 北京时间 | 
                    <i class="fas fa-clock me-1"></i>{{ query_time }}
                </small>
            </div>
        </div>
        
        <div class="text-center text-muted mt-3">
            <small>© 2025 小红书博主 <strong>ZeroFive</strong> 版权所有</small>
        </div>
    </div>
</div>
</body>
</html>
'''

# ===== 启动应用 =====
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=2001, debug=False)