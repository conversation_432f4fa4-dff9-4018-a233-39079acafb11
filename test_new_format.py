#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新API格式的解析
"""

import json

# 你提供的新格式示例
new_format_data = {
    "Uuid": "016cff7e-51d9-43de-958e-677c0e61dc1d",
    "CorrespondingAuthor": "<PERSON><PERSON>",
    "DocumentId": 91824,
    "FirstAuthor": "<PERSON><PERSON><PERSON>",
    "JournalAcronym": "HMT",
    "JournalName": "International Journal of Heat and Mass Transfer",
    "LastUpdated": 1754744192,
    "LatestRevisionNumber": 0,
    "ManuscriptTitle": "An Improved Numerical Model for Ice Crystal Sticking under Mixed-Phase Icing Condition",
    "PubdNumber": "HMT-D-25-03540",
    "Status": 3,
    "SubmissionDate": 1750627742,
    "ReviewSummary": {
        "ReviewsCompleted": "1",
        "ReviewInvitationsAccepted": "2",
        "ReviewInvitationsSent": "2+"
    }
}

def test_new_format_parsing():
    """测试新格式的解析"""
    print("=== 测试新API格式解析 ===")
    print(f"稿件标题: {new_format_data.get('ManuscriptTitle')}")
    print(f"稿件编号: {new_format_data.get('PubdNumber')}")
    print(f"期刊名称: {new_format_data.get('JournalName')}")
    print(f"第一作者: {new_format_data.get('FirstAuthor')}")
    print(f"通讯作者: {new_format_data.get('CorrespondingAuthor')}")
    print(f"状态码: {new_format_data.get('Status')}")
    print(f"最新修订版本: {new_format_data.get('LatestRevisionNumber')}")
    
    review_summary = new_format_data.get("ReviewSummary", {})
    print(f"\n=== 审稿摘要 ===")
    print(f"邀请发送: {review_summary.get('ReviewInvitationsSent')}")
    print(f"邀请接受: {review_summary.get('ReviewInvitationsAccepted')}")
    print(f"审稿完成: {review_summary.get('ReviewsCompleted')}")
    
    # 测试数字解析
    try:
        invited_count = review_summary.get("ReviewInvitationsSent", "0")
        accepted_count = review_summary.get("ReviewInvitationsAccepted", "0")
        completed_count = review_summary.get("ReviewsCompleted", "0")
        
        invited_num = int(invited_count.replace('+', '')) if isinstance(invited_count, str) else int(invited_count)
        accepted_num = int(accepted_count) if isinstance(accepted_count, str) else int(accepted_count)
        completed_num = int(completed_count) if isinstance(completed_count, str) else int(completed_count)
        
        print(f"\n=== 解析后的数字 ===")
        print(f"邀请数量: {invited_num}")
        print(f"接受数量: {accepted_num}")
        print(f"完成数量: {completed_num}")
        
        # 检查RRC状态
        if new_format_data.get("Status") == 3:
            if accepted_num > 0 and completed_num >= accepted_num:
                print(f"✅ 检测到RRC状态：所有接受的审稿人都已完成审稿")
            else:
                print(f"⏳ 审稿进行中：{completed_num}/{accepted_num} 已完成")
        
    except (ValueError, TypeError) as e:
        print(f"❌ 数字解析失败: {e}")

if __name__ == "__main__":
    test_new_format_parsing()
