import numpy as np
import matplotlib.pyplot as plt

# 参数设置
mu = 2e-3         # Pa·s
k = 9.869e-13     # m^2
phi = 0.3
dx = 0.1          # m
dt = 10           # s
L = 1.0           # m
N = 10            # 网格数
Tmax = 5000       # 最大时间
steps = int(Tmax / dt)

# 初始与边界条件
Pin = 1.01325e7   # Pa
Pout = 1.01325e5  # Pa
p = np.full(N+1, Pin)
p[-1] = Pout

# 系数矩阵构建（隐式法）
alpha = k * dt / (mu * phi * dx**2)
A = np.eye(N+1) * (1 + 2 * alpha)
for i in range(1, N):
    A[i, i-1] = -alpha
    A[i, i+1] = -alpha
A[0, :] = 0; A[0, 0] = 1   # 封闭端
A[N, :] = 0; A[N, N] = 1   # 定压端

# 存储过程
results = [p.copy()]
for t in range(steps):
    b = p.copy()
    b[0] = Pin
    b[N] = Pout
    p = np.linalg.solve(A, b)
    if t % 50 == 0:
        results.append(p.copy())

# 绘图
x = np.linspace(0, L, N+1)
for i, res in enumerate(results):
    plt.plot(x, res/1e5, label=f'T={i*dt*50}s')
plt.xlabel('x (m)')
plt.ylabel('P (atm)')
plt.legend()
plt.title('')
plt.grid(True)
plt.show()
