import sys
def solve():
    try:
        input = sys.stdin.readable

        line1=input().strip()

        if not line1:
            return
        n,m=map(int,line1.split())

        total_length =0 

        for _ in range(n):
            x,y=map(int,input().split())

            s_min=min(x,y)
            s_max=max(x,y)

            if s_max<=m:
                total_length +=s_min
            else:
                total_length += s_max
        
        print(total_length)
    except(<PERSON>OError,ValueError):
        return
    
if __name__ == "__main__":
    solve()