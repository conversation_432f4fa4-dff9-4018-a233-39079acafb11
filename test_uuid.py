import requests

uuid = '016cff7e-51d9-43de-958e-677c0e61dc1d'
api_url = f'https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{uuid}'
print(f'API URL: {api_url}')

try:
    response = requests.get(api_url, timeout=10)
    print(f'Status Code: {response.status_code}')
    
    if response.status_code != 200:
        print('Status code is not 200')
        raise ValueError('无效响应')
    
    data = response.json()
    print(f'Data type: {type(data)}')
    has_status = 'Status' in data if isinstance(data, dict) else False
    print(f'Has Status: {has_status}')
    
    if not isinstance(data, dict) or 'Status' not in data:
        print('Data format error')
        raise ValueError('数据格式错误')
    
    print('Success! Data retrieved successfully')
    status = data.get('Status')
    title = data.get('ManuscriptTitle')
    print(f'Status: {status}')
    print(f'Title: {title}')
    
except Exception as e:
    print(f'Error: {e}')
    import traceback
    traceback.print_exc()
