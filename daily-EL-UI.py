# ========== Part 1: 初始化与日志函数 ==========
from flask import Flask, render_template_string, request, redirect, url_for, session, flash
from flask_apscheduler import APScheduler
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Email, NumberRange
import requests
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timezone
import pytz
import json
import hashlib  
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
scheduler = APScheduler()
scheduler.init_app(app)
scheduler.start()

CST = pytz.timezone('Asia/Shanghai')
monitor_tasks = {}
MAX_TASKS = 10



ADMIN_USERNAME = "3313637051"
ADMIN_PASSWORD = "aidi+6898"

def status_text(code):
    mapping = {
        1: "Submitted - 已提交",
        2: "Editor Assigned - 编辑已分配",
        3: "Under Review - 正在审稿中",
        4: "Decision in Progress - 编辑评估中",
        5: "Reviews Complete - 审稿已完成",
        6: "Revision Required - 需要修改",
        7: "Accepted - 已接受",
        8: "Rejected - 已拒稿"
    }
    return mapping.get(code, f"未知状态（{code}）")

def write_log(recipient_email, message, threshold=100):
    masked = recipient_email[:3] + '***' + recipient_email[-3:]
    filename = f"{masked}_log.txt"
    now = datetime.now(CST).strftime('%Y-%m-%d %H:%M:%S')
    new_entry = f"[{now}] {message}\n"
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        else:
            lines = []
        lines.append(new_entry)
        if len(lines) > threshold:
            lines = lines[-threshold:]
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(lines)
    except Exception as e:
        print(f"日志写入错误: {e}")

def send_email(subject, body, sender_email, password, recipient_email):
    msg = MIMEText(body)
    msg['Subject'] = subject
    msg['From'] = sender_email
    msg['To'] = recipient_email
    try:
        if not sender_email.endswith('@qq.com'):
            raise ValueError("仅支持 QQ 邮箱作为发件人")
        server = smtplib.SMTP('smtp.qq.com', 587)
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, [recipient_email], msg.as_string())
        server.quit()
        write_log(recipient_email, f"📧 邮件已发送: {subject}")
        return True
    except Exception as e:
        write_log(recipient_email, f"❌ 邮件发送失败: {e}")
        return False

def format_ts(ts):
    return datetime.fromtimestamp(ts, tz=timezone.utc).astimezone(CST).strftime('%Y-%m-%d %H:%M:%S')

def generate_hash(obj_list):
    filtered = [
        {"Id": ev["Id"], "Revision": ev["Revision"], "Event": ev["Event"], "Date": ev["Date"]}
        for ev in obj_list
    ]
    return hashlib.md5(json.dumps(filtered, sort_keys=True).encode()).hexdigest()

def check_status(task_id):
    task = monitor_tasks.get(task_id)
    if not task:
        return
    try:
        url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{task['uuid']}"
        response = requests.get(url)
        data = response.json()
        if not isinstance(data, dict) or "Status" not in data:
            raise ValueError("响应格式不正确或不包含 Status 字段")

        cur_status = data.get("Status")
        cur_updated = data.get("LastUpdated")
        reviewers_data = data.get("ReviewEvents", [])

        current_review_hash = generate_hash(reviewers_data)
        write_log(task['recipient_email'], f"🔍 定时检查，状态: {status_text(cur_status)}，更新时间: {format_ts(cur_updated)}")

        should_notify = False
        reasons = []

        if task['last_status'] != cur_status:
            should_notify = True
            reasons.append("状态码变化")
        if task['last_updated'] != cur_updated:
            should_notify = True
            reasons.append("更新时间变化")
        if task.get("review_hash") != current_review_hash:
            should_notify = True
            reasons.append("审稿人信息变化")

        revision_max = max([r.get("Revision", 0) for r in reviewers_data], default=0)
        current_round = [r for r in reviewers_data if r.get("Revision", 0) == revision_max]
        reviewer_ids = set(ev["Id"] for ev in current_round if ev["Event"] == "Reviewer Invited")
        completed_ids = set(ev["Id"] for ev in current_round if ev["Event"] in {"Review Completed", "Declined", "Unassigned"})

        is_rrc = reviewer_ids and reviewer_ids.issubset(completed_ids)
        if is_rrc:
            reasons.append("本轮所有审稿人已完成（RRC）")
            should_notify = True

        if should_notify:
            status_str = status_text(cur_status)
            if is_rrc:
                status_str += " + RRC"
            subject = "📢 稿件状态更新通知"
            body = (
                f"论文标题: {data.get('ManuscriptTitle')}\n"
                f"编号: {data.get('PubdNumber')}\n"
                f"当前状态: {status_str}\n"
                f"最近更新时间: {format_ts(cur_updated)}\n"
                f"原因: {'、'.join(reasons)}"
            )
            send_email(subject, body, task['sender_email'], task['password'], task['recipient_email'])
            write_log(task['recipient_email'], f"📢 通知发送，原因: {'、'.join(reasons)}")
            task['last_status'] = cur_status
            task['last_updated'] = cur_updated
            task['review_hash'] = current_review_hash

    except Exception as e:
        write_log(task['recipient_email'], f"❌ UUID 解析失败或请求异常，任务终止 - {e}")
        scheduler.remove_job(task_id)
        del monitor_tasks[task_id]

# ========== Part 2: 表单与视图管理 ==========
class MonitorForm(FlaskForm):
    sender_email = StringField('发件人邮箱', validators=[DataRequired(), Email()])
    password = PasswordField('邮箱授权码', validators=[DataRequired()])
    recipient_email = StringField('收件人邮箱', validators=[DataRequired(), Email()])
    uuid = StringField('稿件 UUID', validators=[DataRequired()])
    interval = IntegerField('检查间隔（分钟）', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('开始监控')

class LoginForm(FlaskForm):
    username = StringField('管理员账号', validators=[DataRequired()])
    password = PasswordField('密码', validators=[DataRequired()])
    submit = SubmitField('登录')

class ConfigForm(FlaskForm):
    max_tasks = IntegerField('最大监控任务数', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('更新设置')

@app.route('/', methods=['GET', 'POST'])
def index():
    global MAX_TASKS
    form = MonitorForm()
    can_submit = len(monitor_tasks) < MAX_TASKS
    if form.validate_on_submit() and can_submit:
        if not form.sender_email.data.endswith('@qq.com'):
            return render_template_string(TEMPLATE, form=form, error="❌ 当前仅支持 QQ 邮箱",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)
        task_id = f"task_{len(monitor_tasks) + 1}"
        task = {
            'sender_email': form.sender_email.data,
            'password': form.password.data,
            'recipient_email': form.recipient_email.data,
            'uuid': form.uuid.data,
            'interval': form.interval.data,
            'last_status': None,
            'last_updated': None,
            'review_hash': None
        }
        try:
            url = f"https://tnlkuelk67.execute-api.us-east-1.amazonaws.com/tracker/{task['uuid']}"
            data = requests.get(url).json()
            task['last_status'] = data.get("Status")
            task['last_updated'] = data.get("LastUpdated")
            task["review_hash"] = generate_hash(data.get("ReviewEvents", []))

            subject = "📡 稿件监控已启动"
            body = (
                f"稿件标题: {data.get('ManuscriptTitle')}\n"
                f"编号: {data.get('PubdNumber')}\n"
                f"当前状态: {status_text(data.get('Status'))}\n"
                f"最近更新时间: {format_ts(data.get('LastUpdated'))}\n"
                f"每 {task['interval']} 分钟自动检测一次。"
            )
            if not send_email(subject, body, task['sender_email'], task['password'], task['recipient_email']):
                return render_template_string(TEMPLATE, form=form, error="❌ 邮件发送失败，请检查发件邮箱或授权码。",
                                              monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                              max_tasks=MAX_TASKS, can_submit=can_submit)
            write_log(task['recipient_email'], f"📡 监控已启动，状态: {status_text(data.get('Status'))}")
        except Exception as e:
            write_log(task['recipient_email'], f"❌ 启动失败: {e}")
            return render_template_string(TEMPLATE, form=form, error=f"❌ 启动失败: {e}",
                                          monitor_tasks=monitor_tasks, admin=session.get("admin", False),
                                          max_tasks=MAX_TASKS, can_submit=can_submit)

        monitor_tasks[task_id] = task
        scheduler.add_job(id=task_id, func=check_status, args=[task_id],
                          trigger='interval', minutes=task['interval'])
        flash("✅ 监测已开始", "success")
        return redirect(url_for('index'))

    return render_template_string(TEMPLATE, form=form, monitor_tasks=monitor_tasks,
                                  admin=session.get("admin", False), max_tasks=MAX_TASKS,
                                  can_submit=can_submit, error=None)

@app.route('/delete/<task_id>', methods=['POST'])
def delete_task(task_id):
    if session.get("admin") and task_id in monitor_tasks:
        scheduler.remove_job(task_id)
        write_log(monitor_tasks[task_id]['recipient_email'], f"🗑️ 管理员删除任务 {task_id}")
        del monitor_tasks[task_id]
    return redirect(url_for('index'))

# ========== Part 3: 管理员与日志接口 ==========
@app.route('/admin', methods=['GET', 'POST'])
def admin():
    form = LoginForm()
    if form.validate_on_submit():
        if form.username.data == ADMIN_USERNAME and form.password.data == ADMIN_PASSWORD:
            session['admin'] = True
            return redirect(url_for('admin_config'))
    return render_template_string(ADMIN_TEMPLATE, form=form)

@app.route('/admin/config', methods=['GET', 'POST'])
def admin_config():
    global MAX_TASKS
    if not session.get("admin"):
        return redirect(url_for('admin'))
    form = ConfigForm()
    if form.validate_on_submit():
        MAX_TASKS = form.max_tasks.data
        return redirect(url_for('index'))
    form.max_tasks.data = MAX_TASKS
    return render_template_string(CONFIG_TEMPLATE, form=form, current=MAX_TASKS)

@app.route('/logout')
def logout():
    session.pop("admin", None)
    return redirect(url_for('index'))

@app.route('/logs/<masked_email>')
def view_log(masked_email):
    if not session.get("admin"):
        return redirect(url_for('admin'))
    filename = f"{masked_email}_log.txt"
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        content = "⚠️ 无日志文件或尚未生成。"
    return render_template_string(LOG_TEMPLATE, masked_email=masked_email, content=content)

TEMPLATE = """
<!DOCTYPE html>
<html><head>
    <title>稿件监控平台</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f4f6f8; }
        .form-control, .btn { font-size: 1rem; }
    </style>
</head><body>
<div class="container my-4">
    <h2 class="text-primary mb-4 text-center">📄 Elsevier 稿件监控平台</h2>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <form method="post">{{ form.hidden_tag() }}
        <div class="mb-3">{{ form.sender_email.label }}{{ form.sender_email(class="form-control") }}</div>
        <div class="mb-3">{{ form.password.label }}{{ form.password(class="form-control") }}</div>
        <div class="mb-3">{{ form.recipient_email.label }}{{ form.recipient_email(class="form-control") }}</div>
        <div class="mb-3">{{ form.uuid.label }}{{ form.uuid(class="form-control") }}</div>
        <div class="mb-3">{{ form.interval.label }}{{ form.interval(class="form-control") }}</div>
        {{ form.submit(class="btn btn-success w-100", disabled=not can_submit) }}
    </form>

    {% if error %}
      <div class="alert alert-danger mt-3" role="alert">
        {{ error }}
      </div>
    {% endif %}

    <hr><h4 class="mt-4">📊 当前监控任务（{{ monitor_tasks|length }}）</h4>
    <ul class="list-group mt-3">
        {% for task_id, task in monitor_tasks.items() %}
        <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
            <div>
                📬 <strong>收件人:</strong> {{ task.recipient_email[:3] + '***' + task.recipient_email[-3:] }} |
                ⏱ <strong>间隔:</strong> {{ task.interval }} 分钟
            </div>
            {% if admin %}
            <div class="d-flex flex-wrap gap-2 mt-2 mt-md-0">
                <form method="post" action="{{ url_for('delete_task', task_id=task_id) }}">
                    <button class="btn btn-danger btn-sm">删除</button>
                </form>
                <a class="btn btn-outline-info btn-sm" href="{{ url_for('view_log', masked_email=task.recipient_email[:3] + '***' + task.recipient_email[-3:]) }}">查看日志</a>
            </div>
            {% endif %}
        </li>
        {% else %}
        <li class="list-group-item">暂无任务</li>
        {% endfor %}
    </ul>

    <div class="mt-4 text-center">
        {% if admin %}
            <a href="{{ url_for('admin_config') }}" class="btn btn-secondary btn-sm">⚙️ 设置最大任务数</a>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm ms-2">退出管理员</a>
        {% else %}
            <a href="{{ url_for('admin') }}" class="btn btn-outline-secondary btn-sm">🔒 管理员登录</a>
        {% endif %}
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body></html>
"""

ADMIN_TEMPLATE = """
<!DOCTYPE html>
<html><head>
    <title>管理员登录</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head><body class="bg-light">
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
    <div class="card shadow-sm p-4" style="width: 100%; max-width: 400px;">
        <h3 class="text-center mb-4 text-primary">🔐 管理员登录</h3>
        <form method="post">{{ form.hidden_tag() }}
            <div class="mb-3">{{ form.username.label(class="form-label") }}{{ form.username(class="form-control") }}</div>
            <div class="mb-3">{{ form.password.label(class="form-label") }}{{ form.password(class="form-control") }}</div>
            {{ form.submit(class="btn btn-primary w-100") }}
        </form>
    </div>
</div></body></html>
"""

CONFIG_TEMPLATE = """
<!DOCTYPE html>
<html><head>
    <title>最大任务配置</title><meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head><body class="bg-light">
<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="text-primary">⚙️ 设置最大监控任务数</h3>
        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger btn-sm">退出管理员</a>
    </div>
    <div class="card shadow-sm p-4">
        <form method="post">{{ form.hidden_tag() }}
            <div class="mb-3">当前最大任务数：<strong>{{ current }}</strong></div>
            <div class="mb-3">{{ form.max_tasks.label(class="form-label") }}{{ form.max_tasks(class="form-control") }}</div>
            {{ form.submit(class="btn btn-success") }}
        </form>
        <div class="mt-3">
            <a href="{{ url_for('index') }}" class="btn btn-secondary btn-sm">返回主页</a>
        </div>
    </div>
</div></body></html>
"""

LOG_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>查看日志 - {{ masked_email }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f4f6f8; }
        .log-box {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 1.5rem;
            font-size: 0.95rem;
            line-height: 1.6;
            overflow-wrap: break-word;
            box-shadow: 0 0 8px rgba(0,0,0,0.05);
        }
        pre {
            white-space: pre-wrap;
            word-break: break-word;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
<div class="container my-4">
    <h3 class="mb-3 text-primary text-center">📜 日志查看：{{ masked_email }}</h3>
    <div class="log-box">
        <pre>{{ content }}</pre>
    </div>
    <div class="text-center mt-4">
        <a href="{{ url_for('index') }}" class="btn btn-secondary btn-sm">返回主页</a>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# ===== 启动 Flask 应用 =====
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=4001)
