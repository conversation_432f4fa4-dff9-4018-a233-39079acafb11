2025-07-19 18:05:18,570 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 18:05:18,574 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-19 18:05:18,575 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-19 18:05:18,575 - __main__ - INFO - 🔧 管理员账号: admin
2025-07-19 18:05:18,590 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-19 18:05:18,592 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 18:05:20,838 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:20] "GET / HTTP/1.1" 200 -
2025-07-19 18:05:28,687 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:28] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:05:32,947 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:32] "GET /admin/login HTTP/1.1" 200 -
2025-07-19 18:05:36,045 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:36] "GET / HTTP/1.1" 200 -
2025-07-19 18:05:38,109 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:38] "GET /query HTTP/1.1" 200 -
2025-07-19 18:05:41,221 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:41] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:05:42,783 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:05:42] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:06:07,695 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:06:07] "GET / HTTP/1.1" 200 -
2025-07-19 18:06:37,889 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:06:37] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:06:58,988 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:06:58] "GET /query HTTP/1.1" 200 -
2025-07-19 18:07:03,086 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:07:03] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:07:04,617 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:07:04] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:08:23,639 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:08:23] "GET / HTTP/1.1" 200 -
2025-07-19 18:08:53,916 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:08:53] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:09:23,926 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:09:23] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:09:31,773 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:09:31] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:09:36,400 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:09:36] "GET /admin/login HTTP/1.1" 200 -
2025-07-19 18:09:42,553 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:09:42] "POST /admin/login HTTP/1.1" 200 -
2025-07-19 18:09:49,214 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:09:49] "POST /admin/login HTTP/1.1" 200 -
2025-07-19 18:10:35,211 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:10:35] "GET / HTTP/1.1" 200 -
2025-07-19 18:10:36,978 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:10:36] "GET /query HTTP/1.1" 200 -
2025-07-19 18:10:39,335 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:10:39] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:10:40,882 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:10:40] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:11:46,648 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:11:46] "GET / HTTP/1.1" 200 -
2025-07-19 18:11:58,378 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 18:11:58,381 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-19 18:11:58,381 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-19 18:11:58,381 - __main__ - INFO - 🔧 管理员账号: admin
2025-07-19 18:11:58,397 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-19 18:11:58,397 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 18:11:59,896 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:11:59] "GET / HTTP/1.1" 200 -
2025-07-19 18:12:16,856 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:16] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:12:27,710 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:27] "GET / HTTP/1.1" 200 -
2025-07-19 18:12:38,577 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:38] "GET /admin/login HTTP/1.1" 200 -
2025-07-19 18:12:46,865 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:46] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:12:51,120 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:51] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-07-19 18:12:51,161 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:51] "GET /admin/dashboard HTTP/1.1" 200 -
2025-07-19 18:12:55,481 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:55] "GET /admin/config HTTP/1.1" 200 -
2025-07-19 18:12:57,541 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:12:57] "GET /admin/dashboard HTTP/1.1" 200 -
2025-07-19 18:13:00,326 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:00] "GET / HTTP/1.1" 200 -
2025-07-19 18:13:02,464 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:02] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:13:16,870 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:16] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:13:30,497 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:30] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:13:42,022 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 18:13:42,027 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-19 18:13:42,027 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-19 18:13:42,028 - __main__ - INFO - 🔧 管理员账号: admin
2025-07-19 18:13:42,044 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-19 18:13:42,046 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 18:13:43,293 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:43] "GET / HTTP/1.1" 200 -
2025-07-19 18:13:46,863 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:46] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:13:47,272 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:47] "GET /query HTTP/1.1" 200 -
2025-07-19 18:13:49,802 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:49] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:13:51,740 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:13:51] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:14:00,871 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:00] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:14:01,659 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:01] "GET / HTTP/1.1" 200 -
2025-07-19 18:14:16,872 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:16] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:14:30,861 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:30] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:14:31,846 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:31] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:14:46,868 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:14:46] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:15:00,864 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:00] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:15:01,841 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:01] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:15:16,873 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:16] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:15:24,113 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:24] "GET /query HTTP/1.1" 200 -
2025-07-19 18:15:26,498 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:26] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:15:28,088 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:28] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:15:30,864 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:15:30] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:16:00,864 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:16:00] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:16:17,871 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:16:17] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:16:30,866 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:16:30] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:17:00,538 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:00] "GET / HTTP/1.1" 200 -
2025-07-19 18:17:16,252 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:16] "GET /admin/dashboard HTTP/1.1" 200 -
2025-07-19 18:17:18,864 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:18] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:17:24,307 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:24] "GET /query HTTP/1.1" 200 -
2025-07-19 18:17:26,563 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:26] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:17:31,872 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:31] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:17:32,014 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:32] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:17:45,779 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:45] "GET /query HTTP/1.1" 200 -
2025-07-19 18:17:48,688 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:48] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:17:50,227 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:50] "GET /result?uuid=016cff7e-51d9-43de-958e-677c0e61dc1d HTTP/1.1" 200 -
2025-07-19 18:17:56,387 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:17:56] "GET /query HTTP/1.1" 200 -
2025-07-19 18:18:14,249 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:18:14] "GET /admin/login HTTP/1.1" 200 -
2025-07-19 18:18:19,867 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:18:19] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:18:21,282 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:18:21] "GET / HTTP/1.1" 200 -
2025-07-19 18:18:32,867 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:18:32] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:18:51,490 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:18:51] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:19:20,861 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:19:20] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:19:21,496 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:19:21] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:19:33,869 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:19:33] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:19:51,491 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:19:51] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:20:21,487 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:21] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:20:21,869 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:21] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:20:34,872 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:34] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:20:51,904 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 18:20:51,908 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-19 18:20:51,908 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-19 18:20:51,908 - __main__ - INFO - 🔧 管理员账号: admin
2025-07-19 18:20:51,926 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-19 18:20:51,926 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 18:20:51,992 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:51] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:20:54,446 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:54] "GET / HTTP/1.1" 200 -
2025-07-19 18:20:57,263 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:20:57] "GET /query HTTP/1.1" 200 -
2025-07-19 18:21:00,647 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:00] "GET /query HTTP/1.1" 200 -
2025-07-19 18:21:02,908 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:02] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:21:04,414 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:04] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-19 18:21:19,687 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:19] "GET / HTTP/1.1" 200 -
2025-07-19 18:21:21,869 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:21] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:21:22,861 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:22] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:21:35,862 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:35] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:21:49,871 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:49] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:21:51,860 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:21:51] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:22:17,775 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:22:17] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:22:20,861 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:22:20] "GET /api/stats HTTP/1.1" 200 -
2025-07-19 18:56:45,185 - apscheduler.scheduler - INFO - Scheduler started
2025-07-19 18:56:45,188 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-19 18:56:45,188 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-19 18:56:45,188 - __main__ - INFO - 🔧 管理员账号: 3313637051
2025-07-19 18:56:45,200 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-19 18:56:45,201 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 18:56:46,248 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:56:46] "GET / HTTP/1.1" 200 -
2025-07-19 18:57:06,978 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:57:06] "GET /admin/dashboard HTTP/1.1" 200 -
2025-07-19 18:57:08,762 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:57:08] "GET / HTTP/1.1" 200 -
2025-07-19 18:57:09,923 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:57:09] "GET /query HTTP/1.1" 200 -
2025-07-19 18:57:12,143 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:57:12] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-19 18:57:13,880 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 18:57:13] "GET /result?uuid=44893f74-9757-438e-8455-c7d9c53eeac0 HTTP/1.1" 200 -
2025-07-27 17:52:27,542 - apscheduler.scheduler - INFO - Scheduler started
2025-07-27 17:52:27,546 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-07-27 17:52:27,546 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-07-27 17:52:27,546 - __main__ - INFO - 🔧 管理员账号: 3313637051
2025-07-27 17:52:27,558 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-07-27 17:52:27,558 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-27 17:52:38,288 - werkzeug - INFO - 127.0.0.1 - - [27/Jul/2025 17:52:38] "GET / HTTP/1.1" 200 -
2025-07-27 17:52:39,983 - werkzeug - INFO - 127.0.0.1 - - [27/Jul/2025 17:52:39] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-27 17:52:41,573 - werkzeug - INFO - 127.0.0.1 - - [27/Jul/2025 17:52:41] "GET /query HTTP/1.1" 200 -
2025-07-27 17:52:43,452 - werkzeug - INFO - 127.0.0.1 - - [27/Jul/2025 17:52:43] "[32mPOST /query HTTP/1.1[0m" 302 -
2025-07-27 17:52:44,522 - werkzeug - INFO - 127.0.0.1 - - [27/Jul/2025 17:52:44] "GET /result?uuid=ce5961fd-9233-46db-a556-35b7e6aaf6e8 HTTP/1.1" 200 -
2025-08-12 21:29:54,677 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 21:29:54,682 - __main__ - INFO - 🚀 启动 Elsevier 稿件监控平台 - 极致版
2025-08-12 21:29:54,682 - __main__ - INFO - 📊 配置信息 - 最大任务数: 15, 最大日志行数: 100
2025-08-12 21:29:54,682 - __main__ - INFO - 🔧 管理员账号: 3313637051
2025-08-12 21:29:54,699 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:2001
 * Running on http://**************:2001
2025-08-12 21:29:54,699 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
